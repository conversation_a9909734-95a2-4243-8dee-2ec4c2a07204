/**
 * 英文翻译文件
 */

import type { TranslationKeys } from './zh'

export const en: TranslationKeys = {
  // 页面标题和元数据
  meta: {
    title: 'AI Voice Assistant',
    description: 'Modern AI-powered voice conversation assistant'
  },

  // 主页面
  page: {
    title: 'AI Voice Assistant'
  },

  // 聊天界面
  chat: {
    title: 'Conversation',
    stopGenerating: 'Stop Generating',
    emptyMessage: 'Start a conversation by typing a message or speaking',
    inputPlaceholder: 'Type a message... (Ctrl+Enter to send)',
    keyboardModeHint: 'Keyboard mode: Enter for new line, Ctrl+Enter to send',
    voiceModeHint: {
      listening: 'Listening... The system will automatically recognize and send after you finish speaking...',
      idle: 'Click the microphone button to start voice input'
    },
    connectionStatus: {
      disconnected: 'Not connected to server...',
      connecting: 'Connecting...',
      connected: 'Connected'
    },
    inputMode: {
      voice: 'Voice Mode',
      keyboard: 'Keyboard Mode',
      voiceButton: 'Voice Mode',
      keyboardButton: 'Keyboard Mode',
      switchToVoice: 'Switch to Voice Mode',
      switchToKeyboard: 'Switch to Keyboard Mode'
    },
    toast: {
      switchedToVoice: 'Switched to Voice Mode',
      switchedToKeyboard: 'Switched to Keyboard Mode',
      voiceModeDescription: 'Voice recognition results will be sent automatically. Click the microphone button to start voice input.',
      keyboardModeDescription: 'Please manually click the send button to send messages.'
    },
    sendButton: {
      disconnected: 'Not connected to server',
      voiceListening: 'Voice mode listening, will send automatically',
      send: 'Send message'
    },
    confirm: {
      switchModeWithInput: 'You have unsent input content. Do you want to send it before switching modes?'
    }
  },

  // 工具调用
  toolCall: {
    title: 'Tool',
    status: {
      running: 'Running',
      success: 'Success',
      error: 'Error',
      waiting: 'Waiting'
    },
    labels: {
      parameters: 'Parameters',
      result: 'Result',
      scrollHint: 'Scroll to view more'
    },
    messages: {
      executing: 'Executing tool call, please wait...'
    },
    summary: {
      toolCalls: ' tool calls',
      executing: ' executing',
      completed: ' completed'
    }
  },

  // Sphere动画状态
  sphere: {
    status: {
      idle: 'Idle',
      listening: 'Listening...',
      speaking: 'Speaking...'
    }
  },

  // 错误消息
  errors: {
    websocket: {
      parseMessage: 'Failed to parse WebSocket message',
      connectionError: 'WebSocket connection error',
      createConnection: 'Failed to create WebSocket connection',
      maxReconnect: 'Max reconnection attempts reached',
      notConnected: 'WebSocket not connected',
      sendMessage: 'Failed to send message'
    },
    toolCall: {
      parseArguments: 'Failed to parse arguments'
    },
    context: {
      appStateProvider: 'useAppState must be used within an AppStateProvider',
      webSocketProvider: 'useWebSocket must be used within a WebSocketProvider'
    },
    deprecated: {
      setMessageHandler: 'setMessageHandler is deprecated, message handling is now automatic'
    }
  },

  // 语言切换
  language: {
    switch: 'Switch Language',
    current: 'Current Language',
    chinese: '中文',
    english: 'English'
  },

  // 通用
  common: {
    send: 'Send',
    cancel: 'Cancel',
    confirm: 'Confirm',
    close: 'Close',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Info'
  }
} as const
