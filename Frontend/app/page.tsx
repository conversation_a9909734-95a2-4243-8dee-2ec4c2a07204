"use client"
import { useState } from "react"
import { Sphere } from "@/components/sphere"
import { ChatInterface } from "@/components/chat-interface"
import { LanguageSwitcher } from "@/components/language-switcher"
import { DynamicHead } from "@/components/dynamic-head"
import { useI18n } from "@/lib/i18n/context"

export default function Home() {
  const { t } = useI18n()
  const [skinTheme, setSkinTheme] = useState<"default" | "alternative" | "happy">("default")
  const [isOptionsOpen, setIsOptionsOpen] = useState(false)
  const [isSkinMenuOpen, setSkinMenuOpen] = useState(false)

  return (
    <>
      <DynamicHead />
      <main className="flex min-h-screen flex-col items-center justify-between">
        {/* 语言切换器 - 右上角 */}
        <div className="absolute top-4 right-4 z-10">
          <div className="relative inline-block text-left">
            <div>
              <button type="button" onClick={() => setIsOptionsOpen(!isOptionsOpen)} className="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-100 focus:ring-indigo-500" id="options-menu" aria-haspopup="true" aria-expanded={isOptionsOpen}>
                选项
                <svg className={`-mr-1 ml-2 h-5 w-5 transform transition-transform duration-200 ${isOptionsOpen ? 'rotate-180' : 'rotate-0'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
            <div className={`origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 transition ease-out duration-100 ${isOptionsOpen ? 'transform opacity-100 scale-100' : 'transform opacity-0 scale-95'}`}>
              <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                <div className="px-4 py-2 text-sm text-gray-700">语言</div>
                <LanguageSwitcher variant="compact" />
                <div className="px-4 py-2 text-sm text-gray-700">皮肤</div>
                <div className="relative inline-block w-full text-left">
                  <button type="button" onClick={() => setSkinMenuOpen(!isSkinMenuOpen)} className="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-100 focus:ring-indigo-500" id="skin-menu" aria-haspopup="true" aria-expanded={isSkinMenuOpen}>
                    <div className="flex items-center">
                      <div className={`w-4 h-4 rounded-full mr-2 ${skinTheme === 'default' ? 'bg-gradient-to-r from-blue-500 to-purple-500' : skinTheme === 'alternative' ? 'bg-gradient-to-r from-purple-500 to-pink-500' : 'bg-gradient-to-r from-green-500 to-blue-500'}`}></div>
                      {skinTheme === "default" ? "蓝紫渐变" : skinTheme === "alternative" ? "紫粉渐变" : "绿蓝渐变"}
                    </div>
                    <svg className={`-mr-1 ml-2 h-5 w-5 transform transition-transform duration-200 ${isSkinMenuOpen ? 'rotate-180' : 'rotate-0'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                  <div className={`origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 transition ease-out duration-100 ${isSkinMenuOpen ? 'transform opacity-100 scale-100' : 'transform opacity-0 scale-95'}`}>
                    <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="skin-menu">
                      <a href="#" onClick={() => { setSkinTheme("default"); setSkinMenuOpen(false); }} className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" role="menuitem">
                        <div className="flex items-center">
                          <div className="w-4 h-4 rounded-full mr-2 bg-gradient-to-r from-blue-500 to-purple-500"></div>
                          蓝紫渐变
                        </div>
                      </a>
                      <a href="#" onClick={() => { setSkinTheme("alternative"); setSkinMenuOpen(false); }} className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" role="menuitem">
                        <div className="flex items-center">
                          <div className="w-4 h-4 rounded-full mr-2 bg-gradient-to-r from-purple-500 to-pink-500"></div>
                          紫粉渐变
                        </div>
                      </a>
                      <a href="#" onClick={() => { setSkinTheme("happy"); setSkinMenuOpen(false); }} className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" role="menuitem">
                        <div className="flex items-center">
                          <div className="w-4 h-4 rounded-full mr-2 bg-gradient-to-r from-green-500 to-blue-500"></div>
                          绿蓝渐变
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="w-full max-w-5xl flex flex-col items-center justify-center px-5 py-10 md:py-24">
          <h1 className="text-4xl font-bold text-center mb-8 text-gray-800 dark:text-gray-100">
            {t.page.title}
          </h1>

          <div className="w-full flex flex-col md:flex-row gap-8 items-center">
            <div className="w-full md:w-1/2 flex justify-center">
              <Sphere skinTheme={skinTheme} />
            </div>

            <div className="w-full md:w-1/2">
              <ChatInterface />
            </div>
          </div>
        </div>
      </main>
    </>
  )
}
