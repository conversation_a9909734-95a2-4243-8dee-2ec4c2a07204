"use client"

/**
 * 重构后的WebSocket上下文
 * 现在直接使用AppStateProvider，这个文件保持向后兼容的接口
 */

import { createContext, useContext, type ReactNode } from "react"
import { useAppState } from "@/components/app-state-provider"

// 定义WebSocket消息类型（保持兼容）
export type WebSocketMessage = {
  type: string
  payload: any
}

// 定义WebSocket上下文类型（保持兼容）
type WebSocketContextType = {
  status: {
    module: string
    status: string
    message?: string
    data?: any
  }
  sendMessage: (message: WebSocketMessage) => boolean
  updateStatus: (status: { module: string, status: string, message?: string, data?: any }) => void
  lastMessage: WebSocketMessage | null
  isConnected: boolean
  connectionError: string | null
  setMessageHandler: (handler: (message: WebSocketMessage) => void) => void
}

// 消息类型常量（保持兼容）
export const MSG_TYPE_USER_TEXT_INPUT = "USER_TEXT_INPUT"
export const MSG_TYPE_INTERRUPT_TURN = "INTERRUPT_TURN"
export const MSG_TYPE_TOGGLE_ASR_LISTENING = "TOGGLE_ASR_LISTENING"
export const MSG_TYPE_SYSTEM_STATUS = "SYSTEM_STATUS"
export const MSG_TYPE_LLM_CHUNK = "LLM_CHUNK"
export const MSG_TYPE_LLM_FINAL_RESPONSE = "LLM_FINAL_RESPONSE"
export const MSG_TYPE_TTS_STATUS = "TTS_STATUS"
export const MSG_TYPE_ASR_FINAL_RESULT = "ASR_FINAL_RESULT"
export const MSG_TYPE_ERROR_MESSAGE = "ERROR_MESSAGE"
export const MSG_TYPE_TURN_ENDED = "TURN_ENDED"
export const MSG_TYPE_TURN_INTERRUPTED = "TURN_INTERRUPTED"
export const MSG_TYPE_TOOL_CALL_START = "TOOL_CALL_START"
export const MSG_TYPE_TOOL_CALL_RESULT = "TOOL_CALL_RESULT"

// 创建WebSocket上下文
const WebSocketContext = createContext<WebSocketContextType | null>(null)

export function WebSocketProvider({ children }: { children: ReactNode }) {
  // 使用新的状态管理器
  const appState = useAppState()
  
  // 从状态管理器获取状态
  const isConnected = appState.state.connection.state === 'connected'
  const connectionError = appState.state.connection.error
  const lastMessage = appState.state.system.lastMessage
  
  // 获取当前状态（兼容旧接口）
  const currentStatus = Object.values(appState.state.system.statuses)[0] || { module: "", status: "" }

  // 兼容接口实现
  const sendMessage = (message: WebSocketMessage): boolean => {
    return appState.sendMessage(message)
  }

  const updateStatus = (status: { module: string, status: string, message?: string, data?: any }) => {
    appState.updateSystemStatus(status)
  }

  const setMessageHandler = (handler: (message: WebSocketMessage) => void) => {
    // 这个功能现在由状态管理器内部处理，这里保持接口兼容
    console.warn('setMessageHandler已弃用，消息处理现在是自动的')
  }

  // 构建上下文值
  const contextValue: WebSocketContextType = {
    status: currentStatus,
    sendMessage,
    updateStatus,
    lastMessage,
    isConnected,
    connectionError,
    setMessageHandler,
  }

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  )
}

export const useWebSocket = () => {
  const context = useContext(WebSocketContext)
  if (!context) {
    throw new Error('useWebSocket必须在WebSocketProvider内部使用')
  }
  return context
}
