#!/bin/bash

# ASR-LLM-TTS 简化启动脚本
# 适用于Linux板卡环境，直接使用系统Python环境
# 使用方式: ./run.sh

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

# 项目目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/Frontend"
BACKEND_DIR="$PROJECT_ROOT/Backend"

echo -e "${BLUE}=== ASR-LLM-TTS 语音对话助手启动 ===${NC}"
echo -e "${YELLOW}项目目录: $PROJECT_ROOT${NC}"

# 增强的清理函数
cleanup() {
    echo -e "\n${YELLOW}收到停止信号，正在清理服务...${NC}"

    # 优先、独立地停止后端服务，确保音频设备被释放
    if pgrep -f "python.*server\.py" > /dev/null; then
        echo -e "${YELLOW}正在停止后端服务...${NC}"
        pkill -f "python.*server\.py" 2>/dev/null

        # 增加循环等待和超时机制
        local timeout=10 # 等待5秒 (10 * 0.5s)
        while [ $timeout -gt 0 ]; do
            if ! pgrep -f "python.*server\.py" > /dev/null; then
                echo -e "${GREEN}✅ 后端服务已成功停止。${NC}"
                timeout=-1 # 标记成功，跳出循环
                break
            fi
            sleep 0.5
            timeout=$((timeout - 1))
        done

        # 如果超时，则强制杀死进程
        if [ $timeout -eq 0 ]; then
            echo -e "${RED}后端服务停止超时，强制清理...${NC}"
            pkill -9 -f "python.*server\.py" 2>/dev/null
            sleep 1 # 等待强制清理生效
            if ! pgrep -f "python.*server\.py" > /dev/null; then
                 echo -e "${GREEN}✅ 后端服务已被强制停止。${NC}"
            else
                 echo -e "${RED}⚠️ 强制停止后端服务失败，请手动检查。${NC}"
            fi
        fi
    else
        echo -e "${YELLOW}未检测到后端服务在运行。${NC}"
    fi

    # 停止前端服务
    # 停止前端服务
    # 生产模式下，进程名通常是 `node .*node_modules/.bin/next start`
    if pgrep -f "next start" > /dev/null; then
        echo -e "${YELLOW}正在停止前端服务...${NC}"
        pkill -f "next start" 2>/dev/null
        sleep 1 # 给前端一点时间响应
        if ! pgrep -f "next start" > /dev/null; then
            echo -e "${GREEN}✅ 前端服务已成功停止。${NC}"
        else
            echo -e "${YELLOW}强制停止前端服务...${NC}"
            pkill -9 -f "next start" 2>/dev/null
        fi
    else
        echo -e "${YELLOW}未检测到前端服务在运行。${NC}"
    fi

    echo -e "${GREEN}清理完成，退出${NC}"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 检查必要命令
if ! command -v python &> /dev/null; then
    echo -e "${RED}错误: 未找到 python 命令${NC}"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo -e "${RED}错误: 未找到 npm 命令${NC}"
    exit 1
fi

# 检查目录
if [ ! -d "$FRONTEND_DIR" ]; then
    echo -e "${RED}错误: 前端目录不存在: $FRONTEND_DIR${NC}"
    exit 1
fi

if [ ! -d "$BACKEND_DIR" ]; then
    echo -e "${RED}错误: 后端目录不存在: $BACKEND_DIR${NC}"
    exit 1
fi

# 启动后端
echo -e "${BLUE}启动后端服务...${NC}"
cd "$BACKEND_DIR"
python server.py > ../backend.log 2>&1 &
BACKEND_PID=$!
echo -e "${GREEN}后端服务已启动 (PID: $BACKEND_PID)${NC}"

# 等待后端启动
echo -e "${YELLOW}等待后端服务启动 (10秒)...${NC}"
sleep 10

# 检查后端是否正常运行
if ! ps -p $BACKEND_PID > /dev/null 2>&1; then
    echo -e "${RED}后端服务启动失败，请检查 backend.log${NC}"
    exit 1
fi

# --- 智能启动前端 ---
echo -e "${BLUE}启动前端服务 (生产模式)...${NC}"
cd "$FRONTEND_DIR"
FRONTEND_LOG="../frontend.log"

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}未检测到 node_modules，正在安装前端依赖...${NC}"
    if ! npm install; then
        echo -e "${RED}❌ 前端依赖安装失败。请检查网络连接或手动在 Frontend 目录运行 npm install。中止启动。${NC}"
        exit 1
    fi
fi

# 检查是否存在有效的构建产物 (BUILD_ID 文件) 或是否需要强制重建
if [ ! -f ".next/BUILD_ID" ] || [ "$1" == "--rebuild" ]; then
  echo -e "${YELLOW}Frontend: Valid production build not found or --rebuild flag specified. Building now... (This may take a moment)${NC}"
  
  # 如果是强制重建，先删除旧的构建
  if [ "$1" == "--rebuild" ]; then
    echo -e "${YELLOW}Frontend: Removing existing build artifacts...${NC}"
    rm -rf .next
  fi
  
  # 执行构建
  if ! npm run build; then
    echo -e "${RED}❌ Frontend build failed. Aborting.${NC}"
    exit 1
  fi
  echo -e "${GREEN}✅ Frontend build completed successfully.${NC}"
else
  echo -e "${GREEN}Frontend: Production build found. Skipping build step.${NC}"
fi

# 启动生产服务器
echo -e "${BLUE}Frontend: Starting in production mode...${NC}"
npm run start > "$FRONTEND_LOG" 2>&1 &
FRONTEND_PID=$!
echo -e "${GREEN}前端服务已启动 (PID: $FRONTEND_PID)${NC}"

# 等待前端启动
echo -e "${YELLOW}等待前端服务启动 (5秒)...${NC}"
sleep 5

# 检查前端是否正常运行
if ! ps -p $FRONTEND_PID > /dev/null 2>&1; then
    echo -e "${RED}前端服务启动失败，请检查 $FRONTEND_LOG ${NC}"
    cleanup
    exit 1
fi

# 获取本机IP地址
get_local_ip() {
    local ip=""

    # 方式1: 优先使用ip route获取默认路由的源IP (最准确)
    if command -v ip &> /dev/null; then
        ip=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' | head -1)
    fi

    # 方式2: 如果上面失败，检查活跃的网络接口
    if [ -z "$ip" ] && command -v ifconfig &> /dev/null; then
        # 优先检查wlan0 (WiFi)，然后是eth0 (有线)
        for interface in wlan0 eth0 enp0s3 ens33; do
            if ifconfig $interface 2>/dev/null | grep -q "inet " && ifconfig $interface 2>/dev/null | grep -q "RUNNING"; then
                ip=$(ifconfig $interface 2>/dev/null | grep 'inet ' | awk '{print $2}' | head -1)
                if [ -n "$ip" ] && [ "$ip" != "127.0.0.1" ]; then
                    break
                fi
            fi
        done
    fi

    # 方式3: 使用ip命令检查活跃接口
    if [ -z "$ip" ] && command -v ip &> /dev/null; then
        for interface in wlan0 eth0 enp0s3 ens33; do
            if ip link show $interface 2>/dev/null | grep -q "state UP"; then
                ip=$(ip addr show $interface 2>/dev/null | grep 'inet ' | awk '{print $2}' | cut -d/ -f1 | head -1)
                if [ -n "$ip" ] && [ "$ip" != "127.0.0.1" ]; then
                    break
                fi
            fi
        done
    fi

    # 方式4: 最后尝试hostname -I
    if [ -z "$ip" ] && command -v hostname &> /dev/null; then
        ip=$(hostname -I | awk '{print $1}' 2>/dev/null)
    fi

    echo "$ip"
}

LOCAL_IP=$(get_local_ip)

echo -e "${GREEN}=== 启动完成 ===${NC}"
echo -e "${BLUE}=== 本地访问地址 ===${NC}"
echo -e "${BLUE}前端地址: http://localhost:3000${NC}"
echo -e "${BLUE}后端地址: http://localhost:8000${NC}"

if [ -n "$LOCAL_IP" ]; then
    echo -e "${BLUE}=== 局域网访问地址 ===${NC}"
    echo -e "${BLUE}前端地址: http://${LOCAL_IP}:3000${NC}"
    echo -e "${BLUE}后端地址: http://${LOCAL_IP}:8000${NC}"
    echo -e "${BLUE}WebSocket: ws://${LOCAL_IP}:8000/ws/chat${NC}"
    echo -e "${YELLOW}局域网内其他设备可使用上述IP地址访问${NC}"
else
    echo -e "${YELLOW}警告: 无法获取局域网IP地址${NC}"
fi

echo -e "${YELLOW}日志文件: backend.log, frontend.log${NC}"
echo -e "${YELLOW}按 Ctrl+C 停止所有服务${NC}"
echo -e "${BLUE}提示: 可以使用 ./status.sh 查看详细状态${NC}"

# 保持脚本运行并监控服务状态
echo -e "\n${BLUE}服务监控中...${NC}"
MONITOR_COUNT=0

while true; do
    # 每30秒显示一次状态信息
    if [ $((MONITOR_COUNT % 6)) -eq 0 ]; then
        echo -e "${BLUE}[$(date '+%H:%M:%S')] 服务运行中... (按 Ctrl+C 停止)${NC}"
    fi

    # 检查进程是否还在运行
    if ! ps -p $BACKEND_PID > /dev/null 2>&1; then
        echo -e "\n${RED}❌ 后端服务意外停止 (PID: $BACKEND_PID)${NC}"
        echo -e "${YELLOW}请检查 backend.log 了解详情${NC}"
        cleanup
        exit 1
    fi

    if ! ps -p $FRONTEND_PID > /dev/null 2>&1; then
        echo -e "\n${RED}❌ 前端服务意外停止 (PID: $FRONTEND_PID)${NC}"
        echo -e "${YELLOW}请检查 frontend.log 了解详情${NC}"
        cleanup
        exit 1
    fi

    MONITOR_COUNT=$((MONITOR_COUNT + 1))
    sleep 5
done
