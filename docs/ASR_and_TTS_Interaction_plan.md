# ASR 与 TTS 交互优化方案：多进程架构

## 问题背景

当前将 ASR 模块 (`asr_module.py` 使用 ALSA) 直接集成在主助手进程 (`main.py`) 中运行时，虽然 CPU 占用率不高，但 TTS 语音播放在 ASR 模式下会出现严重的卡顿和 `output underflow` 警告。键盘输入模式下则无此问题。实验发现，将 ASR 测试脚本和键盘输入的助手分别在两个独立终端（即两个进程）运行时，TTS 播放恢复流畅。

这表明问题根源在于 **同一进程内** 的资源竞争和任务调度冲突。持续运行的后台 ASR 线程（即使 CPU 占用不高）干扰了主 asyncio 事件循环对 TTS 音频流生成和 `AudioNewPlayer` 数据供给的及时调度，导致播放器无法稳定获取音频数据。

## 目标

实现 ASR 语音输入功能，同时保证 TTS 语音播放流畅，并允许用户的语音输入（ASR 结果）能够及时打断正在进行的 TTS 播放。

## 提议方案：多进程架构

采用多进程架构，将 VAD (语音活动检测) 和 ASR 功能拆分到独立的子进程中，通过进程间通信 (IPC) 与主助手进程交互。

### 组件

1.  **VAD & Recorder 进程 (`vad_recorder.py`)**
    *   **职责**: 持续监听麦克风，利用 `sherpa-ncnn` 的端点检测功能进行 VAD，录制检测到的完整语音片段。
    *   **输入**: 麦克风音频流 (ALSA)。
    *   **输出**: 录制好的语音片段 (`bytes`)，通过 `multiprocessing.Queue` (`audio_queue`) 发送给 ASR 服务进程。
    *   **实现**: 循环读取 ALSA 数据，送入 Recognizer 进行端点检测。维护一个短期音频缓冲区。检测到语音结束时，将缓冲区中的完整语音片段发送出去。

2.  **ASR 服务进程 (`asr_service.py`)**
    *   **职责**: 接收录制的音频片段，使用 `sherpa-ncnn` 进行非流式识别，并将识别结果发送回主进程。
    *   **输入**: 从 `audio_queue` 接收音频片段 (`bytes`)。
    *   **输出**: 识别结果文本 (`str`)，通过 `multiprocessing.Queue` (`result_queue`) 发送给主助手进程。
    *   **实现**: 循环等待 `audio_queue`，收到数据后调用 `Recognizer.decode_waves()` 或类似方法进行整段识别。

3.  **主助手进程 (`main.py` 修改版)**
    *   **职责**: 管理 VAD 和 ASR 子进程，通过 IPC 接收 ASR 识别结果，处理对话逻辑 (LLM, TTS, 工具调用)，并确保新的 ASR 结果能打断当前的回合（特别是 TTS 播放）。
    *   **修改**: 
        *   移除直接初始化和管理 `ASRModule` 的代码。
        *   在启动时创建 `audio_queue` 和 `result_queue`。
        *   使用 `multiprocessing.Process` 启动 `vad_recorder` 和 `asr_service` 进程，并将队列传递给它们。
        *   运行一个后台 `asyncio` 任务 (`listen_asr_results`)，该任务异步地等待 `result_queue`。
        *   `listen_asr_results` 收到结果后，执行与原 `input_handler.py` 或 ASR 回调类似的逻辑：触发当前回合的中断事件 (`shared_state.current_turn_stop_event.set()`)，将文本放入 `shared_state.user_input_queue`，并设置 `shared_state.new_input_event` 唤醒主循环。
        *   主循环处理 `new_input_event` 的逻辑保持不变，可以统一处理来自（可能保留的）键盘输入和新的 ASR 输入。
        *   添加程序退出时的逻辑，确保能正常终止 VAD 和 ASR 子进程。

### 进程间通信 (IPC)

*   选用 Python 内置的 `multiprocessing.Queue` 作为初始实现方式，因为它简单易用，无需额外依赖。
    *   `audio_queue`: VAD -> ASR (传递 `bytes`)
    *   `result_queue`: ASR -> Main (传递 `str`)

### 优点

*   **解决核心问题**: 进程隔离，避免了 ASR 活动对主事件循环和 TTS 播放的干扰。
*   **鲁棒性**: 子进程的崩溃或错误不会直接导致主助手进程崩溃。
*   **架构清晰**: 各组件职责分明。

### 潜在挑战

*   **实现复杂度**: 需要编写 VAD、录音、进程管理和 IPC 相关代码。
*   **延迟**: VAD 判断、录音结束、IPC 传输会引入微小的额外延迟，但预计远小于 TTS 卡顿带来的影响。
*   **资源管理**: 需要确保子进程在主程序退出时能被正确清理。
