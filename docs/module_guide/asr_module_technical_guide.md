# ASR模块技术文档

## 概述

ASR（语音识别）模块基于Sherpa-ONNX实现本地实时语音识别，采用多进程架构，集成VAD端点检测，为语音助手提供高性能的语音转文本服务。

## 技术架构

### 核心技术栈
- **识别引擎**: Sherpa-ONNX (本地推理)
- **端点检测**: Silero VAD
- **音频处理**: sounddevice + numpy
- **进程模型**: multiprocessing (独立进程)

### 架构流程
```
┌─────────────────────────────────────────────────────────────┐
│                    主进程 (server.py)                        │
│  ┌─────────────────┐                ┌─────────────────────┐  │
│  │  WebSocket      │                │   turn_manager      │  │
│  │  Handler        │◄──────────────►│                     │  │
│  └─────────────────┘                └─────────────────────┘  │
│           │                                   ▲              │
│           │                                   │              │
│           ▼                                   │              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            multiprocessing.Queue                        │ │
│  │              (asr_result_queue)                         │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                ASR服务进程 (asr_service.py)                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   音频采集       │  │   VAD端点检测    │  │  ASR识别引擎  │ │
│  │  (sounddevice)  │─►│  (Silero VAD)   │─►│(Sherpa-ONNX) │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│           │                     │                   │       │
│           ▼                     ▼                   ▼       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              识别结果队列输出                             │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. ASROnnxService 主服务类
负责音频采集、VAD检测和语音识别的核心逻辑。

**主要功能**:
- 初始化Sherpa-ONNX识别器和Silero VAD
- 音频设备管理和采样率适配
- 实时音频流处理和语音分段
- 识别结果输出到队列

### 2. 音频设备管理
**设备选择策略**:
- 关键词匹配（如"UGREEN"）
- 系统默认设备
- 手动指定设备索引

**采样率处理**:
- 自动检测设备采样率
- 重采样到模型要求的16kHz
- 支持多种音频格式

### 3. VAD端点检测
**检测参数**:
- 最小静音时长: 0.25秒
- 缓冲区大小: 100秒
- 实时语音活动检测

**处理流程**:
```python
# 音频输入 → VAD检测 → 语音分段 → ASR识别
self._vad.accept_waveform(audio_samples)
while not self._vad.empty():
    speech_segment = self._vad.front.samples
    recognition_result = self._recognizer.decode(speech_segment)
```

### 4. 多进程架构
**进程分离**:
- ASR服务运行在独立进程
- 通过multiprocessing.Queue通信
- 避免阻塞主进程事件循环

**通信机制**:
```python
# 启动ASR进程
asr_process = multiprocessing.Process(
    target=run_asr_service,
    args=(config, result_queue)
)

# 接收识别结果
result = result_queue.get()  # {"type": "ASR_FINAL_RESULT", "payload": {...}}
```

## 配置参数

```yaml
asr:
  enabled: true
  sample_rate: 16000
  num_threads: 4
  model_dir: "model/asr"
  model_files:
    tokens: "tokens.txt"
    paraformer: "model.onnx"
  vad_model_path: "model/vad/silero_vad.onnx"
  input_device_keyword: "UGREEN"  # 音频设备关键词
```

## 使用方法

### 基本使用
```python
from asr.asr_service import run_asr_service
import multiprocessing

# 启动ASR服务
config = load_asr_config()
result_queue = multiprocessing.Queue()
asr_process = multiprocessing.Process(
    target=run_asr_service,
    args=(config, None, result_queue)
)
asr_process.start()

# 接收识别结果
result = result_queue.get()
text = result['payload']['text']
```

### 系统集成
```python
# 在turn_manager中处理ASR结果
if data.get("type") == "ASR_FINAL_RESULT":
    text = data["payload"]["text"]
    await handle_user_input(text)  # 触发LLM处理
```

## 性能指标

- **延迟**: < 500ms
- **准确率**: > 95% (中文)
- **CPU使用**: 20-30% (单核)
- **内存占用**: ~200MB
- **模型大小**: Paraformer 40MB + VAD 1MB

## 故障排除

### 常见问题

| 问题 | 解决方案 |
|------|----------|
| 无法找到音频设备 | 1. 检查USB设备连接<br>2. 确认设备关键词配置<br>3. 使用`python -m sounddevice`查看设备 |
| 识别准确率低 | 1. 检查麦克风音量<br>2. 降低环境噪音<br>3. 调整VAD参数 |
| 进程启动失败 | 1. 检查模型文件路径<br>2. 确认依赖库完整<br>3. 查看错误日志 |

### 调试命令
```bash
# 查看音频设备
python -m sounddevice

# 测试ASR模块
python -c "from asr.asr_service import ASROnnxService; print('ASR可用')"

# 音频设备诊断
python test/audio_tests/diagnose_rdkx5_audio.py
```

## 部署和扩展

### 环境要求
- Python 3.8+
- 2GB+ 内存
- USB音频设备

### 依赖安装
```bash
pip install sherpa-onnx sounddevice numpy scipy
```

### 模型部署
```bash
# 下载模型文件
mkdir -p model/asr model/vad
# 下载Paraformer和VAD模型到对应目录
```

### 扩展开发
```python
# 自定义ASR引擎
class CustomASRService(ASROnnxService):
    def _init_models(self):
        # 实现自定义初始化
        pass

# VAD参数调优
vad_config.silero_vad.min_silence_duration = 0.5
vad_config.silero_vad.threshold = 0.5
```

### 最佳实践
- **性能**: 根据CPU核心数调整线程数
- **内存**: 定期清理音频缓冲区
- **延迟**: 优化音频块大小和VAD参数
- **监控**: 集成性能监控和日志记录
