# TTS模块技术文档

## 概述

TTS（语音合成）模块基于火山引擎TTS API实现高质量的文本转语音功能，支持双向流式处理，实现边发边收的实时语音合成，为语音助手提供低延迟、高音质的语音输出。

## 技术架构

### 核心技术栈
- **TTS服务**: 火山引擎 TTS WebSocket API
- **通信协议**: WebSocket + 自定义二进制协议
- **音频格式**: PCM 16kHz 16bit 单声道
- **流式处理**: 双向流式（边发边收）
- **音频播放**: sounddevice + 异步流式播放

### 架构流程
```
┌─────────────────────────────────────────────────────────────┐
│                    turn_manager.py                          │
│  ┌─────────────────┐                ┌─────────────────────┐  │
│  │  LLM流式输出     │                │   TTS播放协调器     │  │
│  │                 │──────text──────►│                     │  │
│  └─────────────────┘     queue      └─────────────────────┘  │
│                                               │              │
│                                               ▼              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              tts_player.py                              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                TTS Client (tts/client.py)                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   WebSocket     │  │   协议处理器     │  │   会话管理    │ │
│  │   连接管理       │  │   (protocol.py) │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│           │                     │                   │       │
│           ▼                     ▼                   ▼       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              双向流式处理                                │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                火山引擎 TTS WebSocket API                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   文本接收       │  │   语音合成       │  │   音频流输出  │ │
│  │                 │  │                 │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│            AudioNewPlayer (utils/audio_new_player.py)       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   音频缓冲       │  │   流式播放       │  │   设备管理    │ │
│  │                 │  │                 │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```
## 核心组件

### 1. TTSClient 主客户端类
负责与火山引擎TTS API的WebSocket通信和会话管理。

**主要功能**:
- WebSocket连接建立和保活
- 会话创建和管理
- 双向流式文本合成
- 音频数据接收和处理

**配置参数**:
```python
{
    "ws_url": "wss://openspeech.bytedance.com/api/v1/tts/ws_binary",
    "app_id": "${VOLCENGINE_TTS_APP_ID}",
    "access_token": "${VOLCENGINE_TTS_ACCESS_TOKEN}",
    "resource_id": "${VOLCENGINE_TTS_RESOURCE_ID}",
    "default_speaker": "zh_female_shuangkuaisisi_moon_bigtts",
    "format": "PCM",
    "sample_rate": 16000
}
```

### 2. 双向流式处理
**核心特性**:
- 双向流式处理（边发边收）
- WebSocket长连接管理
- 会话创建和管理
- 音频数据实时接收

### 3. tts_player 播放协调器
整合TTS合成和音频播放的协调器。

**主要功能**:
- 流式TTS合成和播放
- 文本队列管理
- 音频流处理

### 4. V1.5.0静默调用模式
**智能TTS控制**:
- 工具调用期间不启动TTS
- 多轮工具调用支持（最大5轮）
- 智能TTS启动时机判断
## 配置参数

```yaml
tts:
  volcengine:
    ws_url: "wss://openspeech.bytedance.com/api/v1/tts/ws_binary"
    app_id: "${VOLCENGINE_TTS_APP_ID}"
    access_token: "${VOLCENGINE_TTS_ACCESS_TOKEN}"
    resource_id: "${VOLCENGINE_TTS_RESOURCE_ID}"
  audio:
    format: "PCM"
    sample_rate: 16000
    channels: 1
  synthesis:
    default_speaker: "zh_female_shuangkuaisisi_moon_bigtts"
    namespace: "TTS"
    user_id: "default_user"
  connection:
    max_size: 1048576  # 1MB
    connection_timeout: 10.0
    max_retries: 3
```

### 环境变量
```bash
export VOLCENGINE_TTS_APP_ID="your_app_id"
export VOLCENGINE_TTS_ACCESS_TOKEN="your_access_token"
export VOLCENGINE_TTS_RESOURCE_ID="your_resource_id"
```
## 使用方法

### 基本使用
- 支持简单文本合成
- 流式音频合成
- 双向流式处理

### 与LLM集成
- LLM流式输出直接转TTS
- 文本队列缓冲机制
- 实时语音播放

## 性能指标

- **延迟**: < 1秒 (首字符)
- **实时率**: < 2.0 (合成时间/音频时长)
- **音频质量**: PCM 16kHz 16bit
- **并发支持**: 多会话同时合成

## 故障排除

### 常见问题

| 问题 | 解决方案 |
|------|----------|
| WebSocket连接失败 | 1. 检查网络连接<br>2. 验证认证信息<br>3. 确认服务端点URL |
| 认证失败 | 1. 检查环境变量<br>2. 确认token未过期<br>3. 验证app_id匹配 |
| 音频播放卡顿 | 1. 增加缓冲区大小<br>2. 检查网络带宽<br>3. 优化播放策略 |
| 合成延迟过高 | 1. 分段处理长文本<br>2. 优化网络连接<br>3. 启用音频缓冲 |

### 调试命令
```bash
# 测试TTS连接
python -c "from tts.client import TTSClient; print('TTS可用')"
```

## 部署和扩展

### 环境要求
- Python 3.8+
- 稳定网络连接
- 火山引擎TTS账户

### 最佳实践
- **性能**: 连接池和音频缓冲优化
- **质量**: 音频后处理和优化
- **可靠性**: 重试和错误恢复机制
- **监控**: 性能监控和日志记录

## 总结

TTS模块作为eu03-asr-llm-tts系统的语音合成核心组件，提供了：

### 核心能力
- **双向流式处理**: 边发边收的实时语音合成
- **高质量音频**: 支持多种音色和音频格式
- **智能缓存**: 音频缓存和性能优化
- **可靠连接**: WebSocket连接管理和自动重连

### 技术特色
- **火山引擎集成**: 深度集成火山引擎TTS API
- **协议适配**: 自定义二进制协议处理
- **性能监控**: 完善的指标收集和分析
- **安全防护**: 输入验证、速率限制、访问控制

### 生产就绪
- **RDKX5部署**: 针对板卡优化的部署方案
- **监控**: 性能指标和健康检查
- **缓存**: 智能音频缓存策略
- **测试**: 单元测试、集成测试、性能测试

TTS模块为语音助手提供了高质量的语音输出能力，通过双向流式处理实现了低延迟的实时语音合成，是构建流畅语音交互体验的关键组件。合理的架构设计和优化策略确保了系统的高性能、高可用性和良好的用户体验。
