# 协议常量定义 - 根据服务器期望的值更新
PROTOCOL_VERSION = 0b0001  # 必须为1，和官方示例一致
DEFAULT_HEADER_SIZE = 0b0001  # 必须为1，和官方示例一致

# 消息类型
FULL_CLIENT_REQUEST = 0b0001
AUDIO_ONLY_RESPONSE = 0b1011
FULL_SERVER_RESPONSE = 0b1001
ERROR_INFORMATION = 0b1111

# 消息类型标志位 - 修正为二进制值
MSG_FLAG_NO_SEQ = 0b0000
MSG_FLAG_WITH_EVENT = 0b0100  # 修正：原来可能是十进制100，改为二进制0100
MSG_FLAG_POSITIVE_SEQ = 0b0001  # 非终端包，序列号 > 0
MSG_FLAG_LAST_NO_SEQ = 0b0010  # 最后一个包，无序列号
MSG_FLAG_NEGATIVE_SEQ = 0b0011  # 负序列号，表示最后一个包

# 序列化方式
NO_SERIALIZATION = 0b0000
JSON_SERIALIZATION = 0b0001

# 压缩方式
NO_COMPRESSION = 0b0000
GZIP_COMPRESSION = 0b0001

# 事件类型
EVENT_NONE = 0
EVENT_Start_Connection = 1
EVENT_FinishConnection = 2
EVENT_ConnectionStarted = 50
EVENT_ConnectionFailed = 51
EVENT_ConnectionFinished = 52
EVENT_StartSession = 100
EVENT_FinishSession = 102
EVENT_SessionStarted = 150
EVENT_SessionFinished = 152
EVENT_SessionFailed = 153
EVENT_TaskRequest = 200
EVENT_TTSSentenceStart = 350
EVENT_TTSSentenceEnd = 351
EVENT_TTSResponse = 352
EVENT_ErrMsg = 400  # 添加错误消息事件类型 