import json
import logging
from dataclasses import dataclass
from typing import Optional, Dict, Any, Union
from .constants import *

logger = logging.getLogger(__name__)

@dataclass
class Header:
    """二进制协议头部"""
    protocol_version: int = PROTOCOL_VERSION
    header_size: int = DEFAULT_HEADER_SIZE
    message_type: int = 0
    message_type_flags: int = 0
    serial_method: int = NO_SERIALIZATION
    compression_type: int = NO_COMPRESSION
    reserved_data: int = 0

    def as_bytes(self) -> bytes:
        # 确保所有字段都是有效的4位二进制值
        if self.protocol_version > 0b1111 or self.header_size > 0b1111:
            raise ValueError("协议版本或头部大小超出4位二进制值范围")
        if self.message_type > 0b1111 or self.message_type_flags > 0b1111:
            raise ValueError("消息类型或标志位超出4位二进制值范围")
        if self.serial_method > 0b1111 or self.compression_type > 0b1111:
            raise ValueError("序列化方法或压缩类型超出4位二进制值范围")
            
        return bytes([
            (self.protocol_version << 4) | self.header_size,
            (self.message_type << 4) | self.message_type_flags,
            (self.serial_method << 4) | self.compression_type,
            self.reserved_data
        ])


class ProtocolHandler:
    """二进制协议处理器"""
    def __init__(self):
        self.session_id = None
    
    async def send_event(self, websocket, header: Header, event: int = EVENT_NONE, 
                        session_id: Optional[str] = None, payload: Any = None):
        """发送事件
        
        Args:
            websocket: WebSocket连接
            header: 消息头
            event: 事件类型
            session_id: 会话ID
            payload: 消息内容，可以是字符串、字典或二进制数据
        """
        message = bytearray(header.as_bytes())
        
        # 添加事件类型
        if event != EVENT_NONE:
            message.extend(event.to_bytes(4, "big", signed=True))
            
        # 添加会话ID
        if session_id:
            sid_bytes = session_id.encode()
            message.extend(len(sid_bytes).to_bytes(4, "big", signed=False))
            message.extend(sid_bytes)
            
        # 添加payload
        if isinstance(payload, str):
            payload = payload.encode()
        elif isinstance(payload, dict):
            payload = json.dumps(payload).encode()
            
        if payload:
            message.extend(len(payload).to_bytes(4, "big", signed=False))
            message.extend(payload)
        
        try:
            await websocket.send(bytes(message))
            logger.debug(f"发送事件: {event}")
        except Exception as e:
            logger.error(f"发送事件 {event} 失败: {e}")
            raise

    async def receive_response(self, websocket) -> Dict:
        """接收并解析响应
        
        Args:
            websocket: WebSocket连接
            
        Returns:
            解析后的响应数据字典
        """
        try:
            response = await websocket.recv()
        except Exception as e:
            logger.error(f"接收响应失败: {e}")
            raise
            
        if isinstance(response, str):
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                # 如果不是JSON格式，则返回原始字符串
                return {"text": response}
            
        # 解析二进制响应
        if len(response) < 4:
            logger.error(f"响应数据太短，无法解析: {response}")
            return {"error": "响应数据无效"}
            
        header = response[0:4]
        offset = 4
        
        # 解析头部
        protocol_version = header[0] >> 4
        header_size = header[0] & 0x0f
        message_type = header[1] >> 4
        msg_type_flags = header[1] & 0x0f
        serial_method = header[2] >> 4
        compression_type = header[2] & 0x0f
        reserved = header[3]
        
        logger.debug(f"接收到响应: 消息类型={message_type}, 标志位={msg_type_flags}")
        
        result = {
            "protocol_version": protocol_version,
            "header_size": header_size,
            "message_type": message_type,
            "message_type_flags": msg_type_flags,
            "serial_method": serial_method,
            "compression_type": compression_type,
            "reserved": reserved
        }
        
        # 错误信息响应
        if message_type == ERROR_INFORMATION:
            if offset + 4 <= len(response):
                error_code = int.from_bytes(response[offset:offset+4], "big", signed=True)
                offset += 4
                result["error_code"] = error_code
                
                if offset + 4 <= len(response):
                    payload_len = int.from_bytes(response[offset:offset+4], "big", signed=False)
                    offset += 4
                    
                    if offset + payload_len <= len(response):
                        payload = response[offset:offset+payload_len]
                        
                        if compression_type == GZIP_COMPRESSION:
                            import gzip
                            try:
                                payload = gzip.decompress(payload)
                            except Exception as e:
                                logger.error(f"解压错误响应失败: {e}")
                        
                        if serial_method == JSON_SERIALIZATION:
                            try:
                                payload = json.loads(payload.decode())
                            except:
                                pass
                        
                        result["error_payload"] = payload
            
            return result
        
        # 检查是否包含事件
        has_event = (msg_type_flags & MSG_FLAG_WITH_EVENT) != 0
        
        if has_event:
            if offset + 4 <= len(response):
                result["event"] = int.from_bytes(response[offset:offset+4], "big", signed=True)
                offset += 4
                
                # 读取会话ID
                if offset + 4 <= len(response):
                    sid_len = int.from_bytes(response[offset:offset+4], "big", signed=False)
                    offset += 4
                    
                    if offset + sid_len <= len(response):
                        result["session_id"] = response[offset:offset+sid_len].decode()
                        offset += sid_len
                        # 保存当前会话ID以便后续使用
                        self.session_id = result["session_id"]
            
            # 读取payload
            if offset + 4 <= len(response):
                payload_len = int.from_bytes(response[offset:offset+4], "big", signed=False)
                offset += 4
                
                if offset + payload_len <= len(response):
                    payload = response[offset:offset+payload_len]
                    
                    # 如果是音频数据
                    if message_type == AUDIO_ONLY_RESPONSE and serial_method == NO_SERIALIZATION:
                        result["audio_data"] = payload
                    else:
                        # 尝试解析为JSON
                        if serial_method == JSON_SERIALIZATION:
                            try:
                                result["payload"] = json.loads(payload.decode())
                            except:
                                result["payload"] = payload
                        else:
                            result["payload"] = payload
                            
        # 处理TTSResponse事件(音频数据)的特殊情况
        elif message_type == AUDIO_ONLY_RESPONSE:
            if msg_type_flags in [MSG_FLAG_POSITIVE_SEQ, MSG_FLAG_NEGATIVE_SEQ]:
                # 检查是否有序列号
                if offset + 4 <= len(response):
                    seq_num = int.from_bytes(response[offset:offset+4], "big", signed=True)
                    offset += 4
                    result["sequence"] = seq_num
                
                # 读取音频数据
                if offset + 4 <= len(response):
                    payload_len = int.from_bytes(response[offset:offset+4], "big", signed=False)
                    offset += 4
                    
                    if offset + payload_len <= len(response):
                        result["audio_data"] = response[offset:offset+payload_len]
                        # 这里是音频直接返回二进制数据
                        result["event"] = EVENT_TTSResponse  # 标记为TTSResponse事件
                        
                        # 如果是最后一个包
                        if msg_type_flags == MSG_FLAG_NEGATIVE_SEQ:
                            result["is_last"] = True
        
        return result 