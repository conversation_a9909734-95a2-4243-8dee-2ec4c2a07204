# 语音对话助手后端技术文档

## 1. 后端架构概述

语音对话助手后端采用现代化的异步架构，基于FastAPI和WebSocket实现实时通信，支持流式数据处理。系统集成了语音识别(ASR)、大型语言模型(LLM)和文本转语音(TTS)三大核心功能，形成完整的语音交互闭环。

### 1.1 技术栈

- **Web框架**：FastAPI (异步Python框架)
- **通信协议**：WebSocket (实时双向通信)
- **LLM服务**：火山引擎大模型API (支持Function Calling)
- **TTS服务**：火山引擎TTS WebSocket API (双向流式)
- **ASR引擎**：sherpa-onnx (本地语音识别)
- **音频处理**：sounddevice (实时音频播放)
- **并发模型**：asyncio (异步IO) + multiprocessing (多进程)

### 1.2 架构图

```
┌─────────────┐      WebSocket      ┌──────────────────────────────────────┐
│             │<─────连接/消息─────>│              FastAPI服务              │
│   前端应用   │                    │  (server.py - 消息路由与会话管理)     │
│             │                    └───────────────┬───────────┬───────────┘
└─────────────┘                                    │           │
                                                   ▼           ▼
                                        ┌──────────────┐ ┌─────────────┐
                                        │  对话管理器   │ │   ASR服务   │
                                        │(turn_manager)│ │(多进程运行)  │
                                        └──────┬───────┘ └──────┬──────┘
                                               │                │
                                               ▼                ▼
┌───────────────┐  HTTP/WS   ┌───────────┐     │     ┌─────────────────┐
│ 火山引擎LLM API │<─────────>│ LLM客户端  │<───┘     │ 语音识别处理     │
└───────────────┘            └───────────┘           │ (VAD端点检测)    │
                                                     └─────────────────┘
┌───────────────┐  WebSocket ┌───────────┐    ┌─────────────┐
│ 火山引擎TTS API │<────────>│ TTS客户端  │<──>│ TTS播放器   │
└───────────────┘            └───────────┘    │(tts_player) │
                                              └──────┬──────┘
                                                     │
                                                     ▼
                                              ┌─────────────┐
                                              │ 音频播放器   │
                                              │(AudioPlayer)│
                                              └─────────────┘
```

### 1.3 主要模块关系

- **server.py**：系统入口，管理WebSocket连接，初始化核心组件，路由消息
- **turn_manager.py**：处理单轮对话，协调LLM调用、工具执行和TTS播放
- **tts_player.py**：封装TTS合成和音频播放逻辑
- **LLM客户端**：封装与火山引擎LLM API的通信
- **TTS客户端**：封装与火山引擎TTS API的通信
- **ASR服务**：独立进程运行，处理语音识别，通过队列与主进程通信
- **Function Calling**：工具注册、调用和结果处理机制

## 2. 核心组件详解

### 2.1 LLM客户端 (llm/client.py)

LLM客户端封装了与火山引擎大模型API的通信逻辑，支持流式和非流式请求，处理Function Calling和错误情况。

#### 2.1.1 主要功能

- **流式对话**：`stream_chat()` 方法实现流式请求，返回异步生成器
- **非流式对话**：`chat()` 方法实现完整请求，返回完整响应
- **错误处理**：集成错误码映射和友好提示
- **重试机制**：对网络错误实现指数退避重试

#### 2.1.2 流式请求处理

```python
async def stream_chat(self, messages, stream=True, **kwargs):
    """流式对话，yield每个流块的完整结构"""
    async with httpx.AsyncClient() as client:
        async with client.stream("POST", self.api_url, json=payload) as response:
            if response.status_code != 200:
                # 错误处理...
                yield {"error": error_data, "hint": error_hint}
                return

            # 处理流式响应
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    data = json.loads(line[6:])
                    # 处理数据块...
                    yield processed_chunk
```

#### 2.1.3 错误处理机制

LLM客户端实现了完善的错误处理，包括：

- HTTP错误处理（状态码非200）
- API错误处理（响应中包含error字段）
- 网络错误处理（连接超时、断开等）
- 用户友好的错误提示（通过errors.py中的映射）

### 2.2 TTS客户端 (tts/client.py)

TTS客户端实现了与火山引擎TTS WebSocket API的通信，支持双向流式合成，即边发送文本边接收音频。

#### 2.2.1 主要功能

- **连接管理**：处理WebSocket连接的建立、维护和重连
- **会话管理**：创建和管理TTS合成会话
- **流式合成**：支持单向和双向流式合成
- **协议处理**：通过protocol.py处理复杂的WebSocket消息格式

#### 2.2.2 双向流式合成原理

```python
async def synthesize_stream_bidi(self, text_queue, speaker=None):
    """边发边收的流式TTS合成方法"""
    await self.connect(force_reconnect=True)
    await self.start_session(speaker)

    # 创建发送任务（从队列读取文本并发送）
    send_task = asyncio.create_task(self._send_text_from_queue(text_queue))

    try:
        # 接收并yield音频数据
        async for audio_data in self._receive_audio_stream():
            yield audio_data
    finally:
        # 清理资源
        if not send_task.done():
            send_task.cancel()
        await self.close()
```

双向流式合成允许系统在LLM生成文本的同时进行TTS合成和播放，大大提高了响应速度和用户体验。

### 2.3 音频播放器 (utils/audio_new_player.py)

音频播放器基于sounddevice库实现，提供异步、流式的音频播放功能，具有完善的状态管理和错误处理。

#### 2.3.1 主要功能

- **流式播放**：支持从异步生成器接收音频数据并播放
- **状态管理**：维护播放器状态（空闲、播放中、停止等）
- **缓冲策略**：实现预缓冲机制，确保播放流畅
- **音量控制**：支持实时调整播放音量

#### 2.3.2 异步播放机制

```python
async def play_stream(self, audio_generator, sample_rate, channels):
    """流式播放音频（完全异步）"""
    # 初始化队列和状态
    self._audio_queue = queue.Queue(maxsize=self.buffer_size)
    self._set_state(PlayerState.BUFFERING)

    # 创建音频输入任务（从生成器获取数据并放入队列）
    input_task = asyncio.create_task(self._feed_audio_queue(audio_generator))

    # 等待预缓冲完成或出错
    await self._wait_for_prebuffer_or_error()

    # 创建并启动音频流
    self._stream = sd.OutputStream(
        samplerate=sample_rate,
        channels=channels,
        callback=self._callback,
        finished_callback=self._stream_finished_callback
    )
    self._stream.start()

    # 等待播放完成或被中断
    await self._wait_for_playback_finished_or_stopped()
```

#### 2.3.3 缓冲策略

音频播放器实现了两级缓冲策略：

1. **预缓冲**：在开始播放前，先缓冲一定数量的音频数据，避免开始播放时的卡顿
2. **运行时缓冲**：维护一个固定大小的队列，平衡网络延迟和内存使用

### 2.4 ASR服务 (asr/asr_service.py)

ASR服务基于sherpa-onnx实现本地语音识别，采用多进程架构，集成VAD端点检测。

#### 2.4.1 主要功能

- **实时语音识别**：使用sherpa-onnx进行本地、实时的语音识别
- **VAD端点检测**：使用silero-vad检测语音起止点
- **多进程架构**：独立进程运行，避免阻塞主事件循环
- **设备管理**：智能选择和配置音频输入设备

#### 2.4.2 多进程架构

```python
# 在server.py中启动ASR服务进程
asr_result_queue = multiprocessing.Queue(maxsize=20)
asr_service_process = multiprocessing.Process(
    target=run_asr_service,
    args=(asr_config_obj, None, asr_result_queue),
    name="ASRServiceProcess",
    daemon=True
)
asr_service_process.start()

# 创建监听任务，将ASR结果转发到WebSocket
asr_listener_task = asyncio.create_task(listen_and_forward_asr_results())
```

#### 2.4.3 VAD端点检测原理

VAD（Voice Activity Detection，语音活动检测）用于识别语音的起始和结束点，避免识别静音或背景噪音。系统使用silero-vad模型实现高精度的VAD：

1. 实时处理音频流，检测是否包含语音
2. 当检测到语音开始，开始收集音频数据
3. 当检测到语音结束（静音超过阈值），将收集的音频段发送给ASR引擎
4. ASR引擎处理音频段，返回识别结果

## 3. 通信流程

### 3.1 WebSocket消息类型

系统定义了多种WebSocket消息类型，用于前后端通信：

| 消息类型 | 方向 | 描述 | 载荷示例 |
|---------|------|------|---------|
| USER_TEXT_INPUT | C→S | 用户文本输入 | `{"text": "你好"}` |
| INTERRUPT_TURN | C→S | 中断当前对话 | `{}` |
| TOGGLE_ASR_LISTENING | C→S | 切换ASR监听状态 | `{"enabled": true}` |
| SYSTEM_STATUS | S→C | 系统状态更新 | `{"module": "turn_handler", "status": "processing_started"}` |
| LLM_RESPONSE | S→C | LLM响应内容 | `{"content": "你好，我是助手", "is_complete": false}` |
| TTS_STATUS | S→C | TTS状态更新 | `{"status": "speaking"}` |
| ASR_RESULT | S→C | ASR识别结果 | `{"text": "你好", "is_final": true}` |
| TOOL_CALL_START | S→C | 工具调用开始 | `{"tool": "calculator", "params": {"x": 1, "y": 2, "operation": "add"}}` |
| TOOL_CALL_RESULT | S→C | 工具调用结果 | `{"tool": "calculator", "result": "3"}` |

### 3.2 消息处理流程

```
┌─────────┐     ┌─────────┐     ┌─────────────┐     ┌─────────────┐
│  客户端  │───>│server.py │────>│ turn_manager │───>│  LLM客户端  │
└─────────┘     └─────────┘     └─────────────┘     └─────────────┘
     ▲               │                  │                  │
     │               │                  │                  │
     └───────────────┼──────────────────┼──────────────────┘
                     │                  │
                     ▼                  ▼
               ┌─────────────┐    ┌─────────────┐
               │  TTS播放器  │<───│  TTS客户端  │
               └─────────────┘    └─────────────┘
```

1. 客户端通过WebSocket发送用户输入
2. server.py接收消息并路由到相应处理器
3. turn_manager.py处理单轮对话逻辑
4. LLM客户端调用大模型API，获取响应
5. 如有工具调用，执行工具并将结果返回LLM
6. LLM响应通过WebSocket实时发送给客户端
7. 同时，响应文本被发送到TTS客户端进行合成
8. TTS合成的音频流被发送到音频播放器播放
9. 各组件状态更新通过WebSocket发送给客户端

### 3.3 流式数据处理机制

系统实现了端到端的流式处理，最大化响应速度：

1. **LLM流式输出**：LLM响应以流式方式返回，每个token立即发送给客户端
2. **TTS流式合成**：LLM输出的文本段实时发送给TTS进行合成
3. **音频流式播放**：TTS合成的音频数据块实时播放，无需等待完整音频
4. **并发处理**：LLM生成、TTS合成和音频播放并发进行

这种流水线式处理大大减少了响应延迟，提升用户体验。

## 4. Function Calling工具调用系统

Function Calling（工具调用）是语音对话助手的核心扩展机制，允许大语言模型调用预定义的工具函数来执行具体任务。系统采用模块化设计，支持工具的动态注册、异步执行和结果处理。

### 4.1 系统概述

- **工具优先原则**：当用户请求可以通过工具实现时，优先调用工具而非文本回复
- **异步执行**：所有工具支持异步执行，不阻塞主事件循环
- **智能调用**：通过精心设计的提示词确保LLM正确使用工具
- **完善错误处理**：参数验证、重试机制和用户友好的错误提示

### 4.2 支持的工具类别

#### 4.2.1 基础工具
- **计算器**：四则运算功能

#### 4.2.2 音频控制
- **音乐播放器**：播放本地音乐，支持MP3/WAV/FLAC格式
- **TTS音量控制**：独立控制语音助手说话音量

#### 4.2.3 视觉识别
- **物体检测**：识别和定位画面中的物体
- **连接测试**：测试视觉系统连接状态

#### 4.2.4 机械臂控制
- **基础操作**：抓取、放置、初始化等动作
- **方向控制**：六方向精确移动控制
- **坐标验证**：防止使用错误坐标的安全机制

#### 4.2.5 摄像头云台
- **云台控制**：相对移动、复位、人脸追踪

### 4.3 核心特性

- **多轮工具调用**：支持基于前一工具结果的连续调用
- **参数验证**：严格的类型检查和范围验证
- **网络重试**：自动重试机制和冷却策略
- **实时反馈**：通过WebSocket实时报告工具执行状态

### 4.4 详细文档

关于Function Calling系统的详细实现、工具扩展指南、配置说明等，请参阅：

**📖 [Function Calling 工具调用系统详细文档](../docs/function_call.md)**

该文档包含：
- 完整的架构设计和组件说明
- 所有工具的详细使用方法和参数说明
- 工具调用流程和WebSocket消息协议
- 智能提示词设计和错误处理机制
- 新工具扩展指南和最佳实践
- 性能优化和安全考虑

## 5. 音频设备配置工具

系统提供了专门的音频设备配置工具模块，用于简化Linux环境下的音频设备配置和管理。该模块特别适合需要精确控制USB音频设备的应用场景。

### 5.1 工具概述

音频配置工具模块位于 `utils/audio_config/` 目录，包含3个核心工具：

- **🌟 setup_audio_devices.py** - 主配置工具，提供图形化菜单界面
- **🔍 check_audio_devices.py** - 设备状态检测工具，快速查看配置状态
- **⚙️ audio_device_config_manager.py** - 底层配置管理器，提供核心功能

### 5.2 主要功能

- **智能设备扫描**：自动检测所有可用的PulseAudio输入输出设备
- **多种配置模式**：支持自定义配置、单独设备配置等多种模式
- **安全配置策略**：不自动修改config.yaml，通过提示引导用户手动配置
- **设备状态验证**：自动验证设备配置是否符合项目要求
- **友好用户界面**：使用emoji和颜色标识，提供直观的操作体验

### 5.3 快速使用

#### 配置音频设备
```bash
cd Backend
python utils/audio_config/setup_audio_devices.py
```

#### 检查设备状态
```bash
cd Backend
python utils/audio_config/check_audio_devices.py
```

#### 查询默认设备
```bash
# 查询默认输出设备
pactl get-default-sink

# 查询默认输入设备
pactl get-default-source
```

### 5.4 详细文档

关于音频配置工具的详细使用方法、配置指南、故障排除等，请参阅：

**📖 [音频配置工具详细文档](utils/audio_config/README.md)**

该文档包含：
- 完整的工具使用指南和操作流程
- config.yaml手动配置方法和示例
- 常见设备关键词和配置参数说明
- 故障排除和性能优化建议
- 各种使用场景的具体操作步骤

## 6. 配置管理

系统使用YAML格式的配置文件(`config.yaml`)管理所有组件参数。

### 6.1 配置文件结构

```yaml
# 应用全局配置
app:
  input_mode: "keyboard"  # 输入模式: "keyboard" 或 "asr"
  system_prompt: "你是一个智能助手..."  # LLM系统提示词

# LLM配置
llm:
  volcengine:
    api_key: "your-api-key"
    api_url: "https://api.example.com"
  model:
    name: "doubao-pro-32k-functioncall-241028"
  parameters:
    temperature: 0.7
    top_p: 0.95
    max_tokens: 4096
    stream: true

# TTS配置
tts:
  volcengine:
    app_key: "your-app-key"
    access_key: "your-access-key"
    resource_id: "your-resource-id"
  voice:
    default_speaker: "zh_female_qingxin"
    available_speakers: ["zh_female_qingxin", "zh_male_qingxin"]
  audio:
    format: "pcm"
    sample_rate: 24000

# ASR配置
asr:
  sample_rate: 16000
  channels: 1
  model_dir: "./model/asr/sherpa-onnx-paraformer-zh"
  vad_model_path: "./model/vad/silero_vad.onnx"
  num_threads: 6
  target_microphone_keyword: "UGREEN"

# 音频播放器配置
audio_player:
  device: null  # null表示使用默认设备
  blocksize: 1024
  buffer_size: 50
  min_prebuffer: 2
  volume: 1.0
```

### 6.2 配置加载

配置通过`utils/config_loader.py`加载和解析：

```python
class ConfigLoader:
    def __init__(self, config_path=None):
        if config_path is None:
            config_path = str(Path(__file__).parent.parent / "config.yaml")
        self.config_path = config_path
        self._config = {}
        self.load_config()

    def load_config(self):
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self._config = yaml.safe_load(f)
```

系统为各组件定义了专用的配置类：
- `LLMConfig`：LLM相关配置
- `TTSConfig`：TTS相关配置
- `AudioPlayerConfig`：音频播放器配置
- `ASRConfig`：ASR相关配置
- `YoloConfig`：YOLO控制相关配置

## 7. 代码组织

### 7.1 目录结构

```
Backend/
├── app/                  # 应用核心逻辑
│   ├── shared_state.py   # 共享状态管理
│   ├── tts_player.py     # TTS播放逻辑
│   └── turn_manager.py   # 对话管理
├── asr/                  # 语音识别相关
│   └── asr_service.py    # ASR服务实现
├── llm/                  # LLM相关
│   ├── client.py         # LLM客户端
│   └── errors.py         # 错误处理
├── tts/                  # TTS相关
│   ├── client.py         # TTS客户端
│   └── protocol.py       # WebSocket协议
├── utils/                # 工具类
│   ├── audio_config/     # 音频设备配置工具
│   │   ├── setup_audio_devices.py      # 主配置工具
│   │   ├── check_audio_devices.py      # 设备状态检测
│   │   └── audio_device_config_manager.py # 配置管理器
│   ├── audio_new_player.py # 音频播放器
│   ├── config_loader.py  # 配置加载
│   └── function_call/    # 工具调用相关
│       ├── function_call_tools.py # 工具注册与调用
│       ├── function_schema.py     # 工具Schema
│       ├── function_tools_impl.py # 工具实现
│       ├── remote_yolo_control.py # YOLO控制
│       └── vision_arm_control.py  # 机械臂控制
├── config.yaml           # 配置文件
└── server.py             # 主入口
```

### 7.2 主要文件职责

| 文件 | 职责 |
|------|------|
| server.py | 系统入口，WebSocket服务器，组件初始化 |
| app/turn_manager.py | 对话管理，协调LLM、工具调用和TTS |
| app/tts_player.py | TTS合成和播放逻辑 |
| app/shared_state.py | 共享状态和事件管理 |
| llm/client.py | LLM API客户端 |
| tts/client.py | TTS API客户端 |
| utils/audio_config/setup_audio_devices.py | 音频设备配置主工具 |
| utils/audio_config/check_audio_devices.py | 音频设备状态检测 |
| utils/audio_new_player.py | 音频播放器 |
| utils/config_loader.py | 配置加载和解析 |
| asr/asr_service.py | ASR服务实现 |

## 8. 部署指南

### 8.1 环境要求

- Python 3.8+
- 依赖库：
  - fastapi
  - uvicorn
  - websockets
  - httpx
  - pyyaml
  - numpy
  - sounddevice
  - sherpa-onnx (ASR模式)
  - pygame (音乐播放功能)

### 8.2 音频设备配置

#### 8.2.1 RDKX5开发板音频配置

对于RDKX5开发板，需要正确配置USB音频设备：

- **输出设备**: UACDemoV1.0 USB喇叭
- **输入设备**: UGREEN CM564 USB麦克风

**快速修复脚本**:
```bash
cd Backend
./scripts/fix_rdkx5_audio.sh
```

**手动配置**:
```bash
# 设置USB喇叭为默认输出
pactl set-default-sink alsa_output.usb-Jieli_Technology_UACDemoV1.0_*

# 设置USB麦克风为默认输入
pactl set-default-source alsa_input.usb-BlueTrm_UGREEN_CM564_*
```

#### 8.2.2 音频问题诊断

如果遇到音频问题，可以使用诊断工具：
```bash
cd Backend
python test/audio_tests/diagnose_rdkx5_audio.py
```

详细的音频问题修复指南请参考：`docs/rdkx5_audio_fix_report.md`

### 8.3 安装步骤

1. 安装Python依赖：
   ```bash
   pip install -r requirements.txt
   ```

2. 配置音频设备（RDKX5）：
   ```bash
   cd Backend
   ./scripts/fix_rdkx5_audio.sh
   ```

2. 下载并配置模型：
   - ASR模型：下载sherpa-onnx兼容的模型，放置在`model/asr/`目录
   - VAD模型：下载silero_vad.onnx，放置在`model/vad/`目录

3. 配置`config.yaml`：
   - 设置火山引擎API密钥
   - 配置LLM和TTS参数
   - 设置输入模式（keyboard或asr）
   - 配置音频设备（如需要）

4. 配置API密钥 (重要):
  - 将 `Backend/.env.example` 文件复制一份并重命名为 `Backend/.env`
  - 编辑 `Backend/.env` 文件，填入您真实的火山引擎 LLM 和 TTS API 密钥。
    ```env
    VOLCENGINE_LLM_API_KEY="YOUR_VOLCENGINE_LLM_API_KEY_HERE"
    VOLCENGINE_TTS_APP_ID="YOUR_VOLCENGINE_TTS_APP_ID_HERE"
    VOLCENGINE_TTS_ACCESS_TOKEN="YOUR_VOLCENGINE_TTS_ACCESS_TOKEN_HERE"
    ```
  - **注意**: `Backend/.env` 文件已被加入 `.gitignore`，不会被提交到版本库，以确保您的密钥安全。

### 8.3 启动服务

```bash
cd Backend
uvicorn server:app --host 0.0.0.0 --port 8000
```

或直接运行：

```bash
python server.py
```

### 8.4 常见问题排查

1. **WebSocket连接失败**：
   - 检查网络连接和防火墙设置
   - 确认端口未被占用

2. **LLM API错误**：
   - 验证API密钥是否正确
   - 检查API URL是否可访问
   - 查看日志中的具体错误信息

3. **TTS合成失败**：
   - 确认TTS配置参数正确
   - 检查网络连接
   - 验证音色名称是否有效

4. **ASR识别问题**：
   - 确认麦克风设备正常工作
   - 检查ASR模型文件是否完整
   - 调整`target_microphone_keyword`参数

5. **音频播放问题**：
   - 使用音频配置工具检查设备状态：`python utils/audio_config/check_audio_devices.py`
   - 重新配置音频设备：`python utils/audio_config/setup_audio_devices.py`
   - 确认系统音量设置
   - 尝试调整`blocksize`和`buffer_size`参数
   - 参考[音频配置工具文档](utils/audio_config/README.md)进行详细排查

6. **内存使用过高**：
   - 减小`max_tokens`参数
   - 调整ASR的`num_threads`参数
   - 检查是否有资源泄漏

## 9. 结语

本文档详细介绍了语音对话助手后端的架构设计和实现细节。系统采用现代化的异步架构，集成了ASR、LLM和TTS三大核心功能，实现了流畅的语音交互体验。通过WebSocket实现实时通信，支持流式数据处理，最大化响应速度。Function Calling机制扩展了系统的能力，支持多种工具调用。

开发者可以基于此架构进一步扩展系统功能，如添加新的工具、优化性能、增强错误处理等。