#!/bin/bash
# RDKX5音频问题快速修复脚本
# 用于修复TTS无声音输出和录音设备配置问题
# 
# 使用方法: ./fix_rdkx5_audio.sh
# 作者: AI Assistant
# 日期: 2025-07-21

set -e

echo "🔧 RDKX5音频问题快速修复脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查PulseAudio是否运行
check_pulseaudio() {
    log_info "检查PulseAudio状态..."
    
    if pulseaudio --check; then
        log_success "PulseAudio正在运行"
    else
        log_warning "PulseAudio未运行，尝试启动..."
        pulseaudio --start
        sleep 2
        if pulseaudio --check; then
            log_success "PulseAudio启动成功"
        else
            log_error "PulseAudio启动失败"
            exit 1
        fi
    fi
}

# 显示当前音频设备状态
show_current_status() {
    log_info "当前音频设备状态:"
    echo
    echo "📋 输出设备:"
    pactl list short sinks
    echo
    echo "🎤 输入设备:"
    pactl list short sources | grep -v monitor
    echo
    echo "🔊 默认设备:"
    pactl info | grep "Default"
    echo
}

# 修复输出设备配置
fix_output_device() {
    log_info "修复输出设备配置..."
    
    # USB喇叭设备名称
    USB_SPEAKER="alsa_output.usb-Jieli_Technology_UACDemoV1.0_5035988353193C1F-00.analog-stereo"
    
    # 检查USB喇叭是否存在
    if pactl list short sinks | grep -q "UACDemoV1.0"; then
        log_info "找到USB喇叭设备，设置为默认输出..."
        
        # 激活设备配置文件
        pactl set-card-profile alsa_card.usb-Jieli_Technology_UACDemoV1.0_5035988353193C1F-00 output:analog-stereo 2>/dev/null || true
        
        # 设置为默认输出设备
        pactl set-default-sink "$USB_SPEAKER"
        
        # 验证设置
        if pactl info | grep "Default Sink" | grep -q "UACDemoV1.0"; then
            log_success "USB喇叭已设置为默认输出设备"
        else
            log_error "设置默认输出设备失败"
            return 1
        fi
    else
        log_error "未找到USB喇叭设备 (UACDemoV1.0)"
        log_info "请检查USB喇叭是否正确连接"
        return 1
    fi
}

# 修复输入设备配置
fix_input_device() {
    log_info "修复输入设备配置..."
    
    # USB麦克风设备名称
    USB_MIC="alsa_input.usb-BlueTrm_UGREEN_CM564_USB_Audio_20220121000002-00.mono-fallback"
    
    # 检查USB麦克风是否存在
    if pactl list short sources | grep -q "UGREEN"; then
        log_info "找到USB麦克风设备，设置为默认输入..."
        
        # 设置为默认输入设备
        pactl set-default-source "$USB_MIC" 2>/dev/null || true
        
        log_success "USB麦克风配置完成"
    else
        log_warning "未找到USB麦克风设备 (UGREEN)"
        log_info "录音功能可能受影响"
    fi
}

# 设置音量
set_volumes() {
    log_info "设置音量..."
    
    # 设置输出音量为100%
    pactl set-sink-volume @DEFAULT_SINK@ 100% 2>/dev/null || true
    
    # 设置输入音量为80%
    pactl set-source-volume @DEFAULT_SOURCE@ 80% 2>/dev/null || true
    
    # 确保设备未静音
    pactl set-sink-mute @DEFAULT_SINK@ false 2>/dev/null || true
    pactl set-source-mute @DEFAULT_SOURCE@ false 2>/dev/null || true
    
    # 设置ALSA音量
    amixer set PCM 100% 2>/dev/null || true
    amixer set Capture 80% 2>/dev/null || true
    amixer set Capture unmute 2>/dev/null || true
    
    log_success "音量设置完成"
}

# 测试音频功能
test_audio() {
    log_info "测试音频功能..."
    
    # 检查是否有Python环境
    if ! command -v python3 &> /dev/null; then
        log_warning "未找到Python3，跳过音频测试"
        return
    fi
    
    # 检查是否在正确目录
    if [ ! -f "test/audio_tests/diagnose_rdkx5_audio.py" ]; then
        log_warning "未找到音频测试脚本，跳过测试"
        log_info "请在Backend目录下运行此脚本"
        return
    fi
    
    log_info "运行音频诊断测试..."
    python3 test/audio_tests/test_recording_simple.py || log_warning "音频测试失败，请手动检查"
}

# 创建持久化配置
create_persistent_config() {
    log_info "创建持久化配置..."
    
    # 创建用户PulseAudio配置目录
    mkdir -p ~/.config/pulse
    
    # 创建默认配置文件
    cat > ~/.config/pulse/default.pa << EOF
# RDKX5音频设备配置
# 自动生成于 $(date)

# 加载默认配置
.include /etc/pulse/default.pa

# 设置默认输出设备为USB喇叭
set-default-sink alsa_output.usb-Jieli_Technology_UACDemoV1.0_5035988353193C1F-00.analog-stereo

# 设置默认输入设备为USB麦克风
set-default-source alsa_input.usb-BlueTrm_UGREEN_CM564_USB_Audio_20220121000002-00.mono-fallback
EOF
    
    log_success "持久化配置已创建: ~/.config/pulse/default.pa"
}

# 显示修复结果
show_results() {
    echo
    log_success "修复完成！当前状态:"
    echo
    show_current_status
    
    echo "✅ 修复项目:"
    echo "   - USB喇叭设置为默认输出设备"
    echo "   - USB麦克风设置为默认输入设备"
    echo "   - 音量设置为合适水平"
    echo "   - 创建持久化配置"
    echo
    echo "🎯 预期效果:"
    echo "   - TTS语音可以正常播放"
    echo "   - ASR录音功能正常"
    echo "   - 配置在重启后保持"
    echo
}

# 主函数
main() {
    echo "开始修复RDKX5音频问题..."
    echo
    
    # 显示当前状态
    show_current_status
    
    # 执行修复步骤
    check_pulseaudio
    fix_output_device
    fix_input_device
    set_volumes
    create_persistent_config
    
    # 显示结果
    show_results
    
    # 可选测试
    read -p "是否运行音频测试? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        test_audio
    fi
    
    log_success "RDKX5音频修复完成！"
}

# 运行主函数
main "$@"
