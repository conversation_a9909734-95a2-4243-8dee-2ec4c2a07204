# ASR-LLM-TTS 后端依赖 (版本锁定)
# Python >= 3.8

# Web框架
fastapi==0.115.6
uvicorn[standard]==0.32.1

# 通信
websockets==13.1
websocket-client==1.8.0
httpx==0.28.1
httpx-sse==0.4.0

# 配置
pyyaml==6.0.2
python-dotenv==1.0.1

# 数据处理
numpy==1.26.4

# 音频处理
sounddevice==0.5.1
soundfile==0.12.1
pydub==0.25.1
pygame==2.6.1

# 语音识别
sherpa-onnx==1.10.30

# MCP支持
mcp==1.1.0
fastmcp==2.10.5
sse-starlette==1.8.2

# 工具
typing-extensions==4.12.2

# 系统依赖: sudo apt-get install ffmpeg libasound2-dev portaudio19-dev
