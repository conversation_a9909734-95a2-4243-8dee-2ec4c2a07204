#!/usr/bin/env python3
"""
MCP工具启动器
提供便捷的MCP相关功能访问
"""

import sys
import os
import argparse
from pathlib import Path

def show_help():
    """显示帮助信息"""
    print("🔧 MCP工具启动器")
    print("=" * 50)
    print("描述:")
    print("  ASR-LLM-TTS项目的MCP (Model Context Protocol) 管理工具")
    print("  提供配置管理、服务器测试和核心服务启动功能")
    print()
    print("可用命令:")
    print("  config  - MCP配置管理")
    print("  test    - MCP服务器测试")
    print("  core    - 启动MCP核心工具服务器")
    print()
    print("使用方法:")
    print("  python mcp_tools.py <command> [options]")
    print("  python mcp_tools.py --help")
    print()
    print("命令详情:")
    print()
    print("📋 config - MCP配置管理")
    print("  python mcp_tools.py config list")
    print("    列出所有MCP服务器配置")
    print()
    print("  python mcp_tools.py config add <name> <type> [options]")
    print("    添加新的MCP服务器")
    print("    类型: streamable-http, sse, stdio")
    print("    示例: python mcp_tools.py config add myserver sse --url https://example.com/sse")
    print()
    print("  python mcp_tools.py config enable <name>")
    print("    启用指定的MCP服务器")
    print()
    print("  python mcp_tools.py config disable <name>")
    print("    禁用指定的MCP服务器")
    print()
    print("  python mcp_tools.py config remove <name>")
    print("    删除指定的MCP服务器")
    print()
    print("🧪 test - MCP服务器测试")
    print("  python mcp_tools.py test")
    print("    测试所有活跃的MCP服务器连接和工具获取")
    print()
    print("🚀 core - 启动MCP核心工具服务器")
    print("  python mcp_tools.py core")
    print("    启动本地MCP核心工具服务器")
    print()
    print("示例:")
    print("  # 查看所有MCP服务器")
    print("  python mcp_tools.py config list")
    print()
    print("  # 添加Context7服务器")
    print("  python mcp_tools.py config add context7 streamable-http \\")
    print("    --url https://mcp.context7.com/mcp \\")
    print("    --description 'Context7文档检索服务器'")
    print()
    print("  # 测试所有服务器")
    print("  python mcp_tools.py test")
    print()
    print("  # 启用/禁用服务器")
    print("  python mcp_tools.py config enable context7")
    print("  python mcp_tools.py config disable context7")
    print()
    print("配置文件位置:")
    print("  utils/mcp/mcp_servers.json")
    print()
    print("更多帮助:")
    print("  python mcp_tools.py config --help")

def main():
    """主函数"""
    # 检查帮助参数
    if len(sys.argv) == 1 or (len(sys.argv) == 2 and sys.argv[1] in ['-h', '--help', 'help']):
        show_help()
        return

    command = sys.argv[1]
    mcp_dir = Path(__file__).parent / "utils" / "mcp"

    if command == "config":
        # 运行配置管理器
        print("🔧 启动MCP配置管理器...")
        config_dir = mcp_dir / "config"
        os.chdir(config_dir)
        args = " ".join(sys.argv[2:]) if len(sys.argv) > 2 else "list"
        os.system(f"python mcp_config_manager.py {args}")

    elif command == "test":
        # 运行测试脚本
        print("🧪 启动MCP服务器测试...")
        tools_dir = mcp_dir / "tools"
        os.chdir(tools_dir)
        os.system("python final_mcp_test.py")

    elif command == "core":
        # 启动核心工具服务器
        print("🚀 启动MCP核心工具服务器...")
        server_dir = mcp_dir / "server"
        os.chdir(server_dir)
        os.system("python mcp_core_tools.py")

    else:
        print(f"❌ 未知命令: {command}")
        print()
        print("可用命令: config, test, core")
        print("使用 'python mcp_tools.py --help' 查看详细帮助")

if __name__ == "__main__":
    main()
