import asyncio
import websockets
import json
import logging

# Configure basic logging for the client
logging.basicConfig(level=logging.INFO, format='%(asctime)s - CLIENT - %(levelname)s - %(message)s')
logger = logging.getLogger("WS_CLIENT")

SERVER_URI = "ws://localhost:8000/ws/chat"

# Server to Client (S2C) Message Types (for parsing responses)
MSG_TYPE_SYSTEM_STATUS = "SYSTEM_STATUS"
MSG_TYPE_LLM_CHUNK = "LLM_CHUNK"
MSG_TYPE_LLM_FINAL_RESPONSE = "LLM_FINAL_RESPONSE"
MSG_TYPE_TTS_STATUS = "TTS_STATUS"
MSG_TYPE_ASR_PARTIAL_RESULT = "ASR_PARTIAL_RESULT"
MSG_TYPE_ASR_FINAL_RESULT = "ASR_FINAL_RESULT"
MSG_TYPE_ERROR_MESSAGE = "ERROR_MESSAGE"
MSG_TYPE_TURN_ENDED = "TURN_ENDED"
MSG_TYPE_TURN_INTERRUPTED = "TURN_INTERRUPTED"
MSG_TYPE_TOOL_CALL_START = "TOOL_CALL_START"
MSG_TYPE_TOOL_CALL_RESULT = "TOOL_CALL_RESULT"


async def listen_to_server(websocket):
    """Listen for messages from the server and print them."""
    try:
        while True:
            message_str = await websocket.recv()
            try:
                message_json = json.loads(message_str)
                logger.info(f"Received from server: {json.dumps(message_json, indent=2, ensure_ascii=False)}")
                # You can add more specific handling here based on message_json['type']
            except json.JSONDecodeError:
                logger.info(f"Received raw from server (not JSON): {message_str}")
    except websockets.exceptions.ConnectionClosedOK:
        logger.info("Connection closed by server.")
    except websockets.exceptions.ConnectionClosedError as e:
        logger.error(f"Connection closed with error: {e}")
    except Exception as e:
        logger.error(f"Error while listening to server: {e}", exc_info=True)

async def send_user_input(websocket):
    """Allow user to send text input to the server."""
    logger.info("Type your message and press Enter to send. Type 'exit' to close.")
    while True:
        try:
            user_message = await asyncio.to_thread(input, "You: ") # Run input in a separate thread
            if user_message.lower() == 'exit':
                logger.info("Exiting client...")
                break
            
            if user_message:
                message_to_send = {
                    "type": "USER_TEXT_INPUT", # Using the constant defined in server.py (or re-defined here)
                    "payload": {"text": user_message}
                }
                await websocket.send(json.dumps(message_to_send))
                logger.info(f"Sent to server: {json.dumps(message_to_send)}")
            
        except EOFError: # Handle Ctrl+D
            logger.info("EOF received, exiting client...")
            break
        except KeyboardInterrupt: # Handle Ctrl+C
            logger.info("Keyboard interrupt received, exiting client...")
            break
        except Exception as e:
            logger.error(f"Error while sending message: {e}", exc_info=True)
            break # Exit on other errors too to prevent loops

async def main():
    logger.info(f"Attempting to connect to WebSocket server at {SERVER_URI}...")
    try:
        async with websockets.connect(SERVER_URI) as websocket:
            logger.info("Successfully connected to WebSocket server.")
            
            # Create tasks for listening to the server and sending user input
            listen_task = asyncio.create_task(listen_to_server(websocket))
            send_task = asyncio.create_task(send_user_input(websocket))
            
            # Wait for either task to complete (e.g., if send_user_input exits)
            done, pending = await asyncio.wait(
                [listen_task, send_task],
                return_when=asyncio.FIRST_COMPLETED,
            )
            
            for task in pending:
                task.cancel() # Cancel any pending tasks
            
            logger.info("Client tasks finished.")

    except websockets.exceptions.InvalidURI:
        logger.error(f"Invalid WebSocket URI: {SERVER_URI}")
    except ConnectionRefusedError:
        logger.error(f"Connection refused by the server at {SERVER_URI}. Is the server running?")
    except Exception as e:
        logger.error(f"Failed to connect or an error occurred: {e}", exc_info=True)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Client shutdown gracefully.") 