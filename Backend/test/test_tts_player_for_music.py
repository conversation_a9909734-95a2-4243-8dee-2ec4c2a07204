# -*- coding: utf-8 -*-
# 这是一个用于测试 AudioNewPlayer 播放本地音乐文件的脚本。
# 目的是验证 sounddevice 是否能成功播放解码后的 MP3 文件，以排查 pygame 的播放问题。
#
# 使用前请确保已安装 pydub 库:
# pip install pydub
#
# pydub 可能需要 ffmpeg 或 libav 支持，如果遇到问题，请根据您操作系统的提示进行安装。
# 例如在 Ubuntu/Debian 上: sudo apt-get install ffmpeg
# 在 macOS 上 (使用 Homebrew): brew install ffmpeg

import asyncio
import dataclasses
import logging
import os
import sys
from pydub import AudioSegment

# 将项目根目录添加到 Python 路径中，以便正确导入项目模块
# 这使得脚本可以从任何位置运行，并能找到 Backend 目录下的模块
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_ROOT)

from Backend.utils.config_loader import ConfigLoader
from Backend.utils.audio_new_player import AudioNewPlayer

# --- 日志配置 ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    stream=sys.stdout
)

# --- 常量定义 ---
# 获取 Backend 目录的绝对路径
BACKEND_DIR = os.path.join(PROJECT_ROOT, 'Backend')
# 定义音乐文件的路径
MP3_FILE_PATH = os.path.join(BACKEND_DIR, 'resource', '彩虹.mp3')
# 定义数据块大小
CHUNK_SIZE = 4096

async def audio_data_generator(pcm_data, chunk_size):
    """
    一个异步生成器，将 PCM 音频数据分块并 yield 出来。
    这是 play_stream 方法需要的输入格式。
    """
    logging.info("音频数据生成器已创建。")
    try:
        for i in range(0, len(pcm_data), chunk_size):
            chunk = pcm_data[i:i + chunk_size]
            if not chunk:
                break
            yield chunk
            # 短暂休眠，将控制权交还给事件循环，避免阻塞
            await asyncio.sleep(0)
        logging.info("所有音频数据块都已生成。")
    except Exception as e:
        logging.error(f"音频数据生成器发生错误: {e}")


async def main():
    """
    主函数，执行加载、解码和播放的整个流程。
    """
    logging.info("--- 开始执行音乐播放测试脚本 ---")

    if not os.path.exists(MP3_FILE_PATH):
        logging.error(f"错误：找不到音频文件，请检查路径是否正确: {MP3_FILE_PATH}")
        return

    try:
        # 1. 加载配置
        logging.info("正在加载配置文件...")
        config_loader = ConfigLoader(config_path=os.path.join(BACKEND_DIR, 'config.yaml'))
        player_config_obj = config_loader.get_audio_player_config()
        logging.info(f"播放器配置加载成功: {player_config_obj}")
        # 确保使用了正确的设备
        if not player_config_obj.device_keyword or player_config_obj.device_keyword != 'UACDemoV1.0':
            logging.warning("警告：配置文件中 'device_keyword' 不是 'UACDemoV1.0'，可能无法使用预期设备。")

        # 2. 使用 pydub 加载和解码 MP3
        logging.info(f"正在加载和解码 MP3 文件: {MP3_FILE_PATH}")
        audio_segment = AudioSegment.from_mp3(MP3_FILE_PATH)
        logging.info("MP3 文件加载和解码成功。")

        # --- 新增代码：重采样音频以匹配设备要求 ---
        # 从 config.yaml 中我们知道，TTS 和 UACDemoV1.0 设备期望的采样率是 48000 Hz
        # 为了解决 "Invalid sample rate" 错误，我们需要将音频重采样到目标采样率
        target_sample_rate = 48000
        if audio_segment.frame_rate != target_sample_rate:
            logging.info(f"音频采样率 ({audio_segment.frame_rate} Hz) 与目标采样率 ({target_sample_rate} Hz) 不匹配。")
            logging.info("正在重采样音频...")
            audio_segment = audio_segment.set_frame_rate(target_sample_rate)
            logging.info("音频重采样成功。")

        # 3. 提取 PCM 数据和音频参数
        pcm_data = audio_segment.raw_data
        sample_rate = audio_segment.frame_rate
        channels = audio_segment.channels
        logging.info(f"音频参数: 采样率={sample_rate}, 通道数={channels}, 数据长度={len(pcm_data)}字节")

        # 4. 实例化播放器
        logging.info("正在实例化 AudioNewPlayer...")
        # 将 dataclass 转换为字典以用于关键字参数解包
        player_config_dict = dataclasses.asdict(player_config_obj)
        player = AudioNewPlayer(**player_config_dict)
        logging.info("AudioNewPlayer 实例化成功。")

        # 5. 创建音频数据生成器
        data_gen = audio_data_generator(pcm_data, CHUNK_SIZE)

        # 6. 播放音频流
        logging.info("准备开始播放音频流...")
        await player.play_stream(data_gen, sample_rate, channels)
        logging.info("音频播放完成。")

    except FileNotFoundError:
        logging.error(f"错误：pydub 依赖的 ffmpeg 或 avconv 未找到。请确保已正确安装。")
    except Exception as e:
        logging.error(f"在执行过程中发生未知错误: {e}", exc_info=True)
    finally:
        logging.info("--- 测试脚本执行结束 ---")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logging.info("用户中断了脚本执行。")