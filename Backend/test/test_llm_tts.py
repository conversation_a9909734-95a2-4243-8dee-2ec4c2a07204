import asyncio
import logging
import queue
import threading
import sys
import os
from typing import Optional, List, Dict, Any
import json

# Assuming your project structure allows these imports
# Adjust paths if necessary
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from llm.client import LLMClient
from llm.errors import LLMClientError
from tts.client import TTSClient
from utils.audio_new_player import AudioNewPlayer, AudioPlayerError, PlayerState
from utils.config_loader import load_config # Only import load_config
from utils.function_call.function_call_tools import FunctionRegistry, music_player_instance
from utils.function_call.function_schema import get_all_schemas

# --- Configuration ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'
)
logger = logging.getLogger("LLM_TTS_TEST")

# Load configurations
try:
    # Assuming a general config file exists at the root
    config_loader = load_config() # Get the ConfigLoader instance
    llm_config_dict = config_loader.get_config().get('llm', {}) # Get raw dict for LLMClient
    tts_config_obj = config_loader.get_tts_config() # Get TTSConfig dataclass
    # audio_player_config = config_loader.get_audio_player_config() # Get AudioPlayerConfig if needed

except FileNotFoundError:
    logger.error("Configuration file not found. Please ensure config.yaml exists in the project root.")
    sys.exit(1)
except Exception as e:
    logger.error(f"Error loading configuration: {e}")
    sys.exit(1)

# Audio settings (usually from TTS config or defaults)
TTS_SAMPLE_RATE = tts_config_obj.sample_rate
TTS_CHANNELS = 1 # Assuming mono output from TTS

# --- Global State for Interrupt Handling ---
new_input_event = asyncio.Event()
user_input_queue = queue.Queue(maxsize=1) # 改回标准库的 queue.Queue，用于线程间通信
current_turn_stop_event: Optional[asyncio.Event] = None
main_loop_is_running = True
main_loop = None  # 存储主事件循环的引用

# --- Input Listener Thread ---
def input_listener():
    """Listens for user input in a separate thread."""
    global current_turn_stop_event, main_loop_is_running, main_loop
    logger.info("Input listener thread started. Press Enter after typing your message.")
    while main_loop_is_running:
        try:
            # Use input() which blocks the thread, but not the main async loop
            text = input("You: ")
            if not main_loop_is_running:
                break
            if text:
                logger.info(f"Input received: '{text}'")
                # Signal interruption to the current turn if one is active
                if current_turn_stop_event and not current_turn_stop_event.is_set():
                    logger.info("Setting stop event for the current turn.")
                    # Schedule setting the event in the main loop
                    if main_loop:
                        # 使用 call_soon_threadsafe 直接设置事件，无需协程
                        main_loop.call_soon_threadsafe(current_turn_stop_event.set)
                    else:
                        logger.error("主事件循环未初始化，无法设置停止事件")

                # Put the new input into the thread-safe queue
                try:
                    # 使用标准库的 queue.Queue 的 put_nowait
                    user_input_queue.put_nowait(text)
                except queue.Full:
                    logger.warning("Input queue full, discarding previous unhandled input.")
                    # Clear and retry putting
                    try:
                        user_input_queue.get_nowait()
                    except queue.Empty:
                        pass
                    user_input_queue.put_nowait(text)

                # Signal the main loop that new input is ready
                logger.info("Signaling new input event.")
                if main_loop:
                    # 使用 call_soon_threadsafe 直接设置事件，无需协程
                    main_loop.call_soon_threadsafe(new_input_event.set)
                else:
                    logger.error("主事件循环未初始化，无法设置新输入事件")
            else:
                 logger.info("Empty input received, ignoring.")

        except EOFError: # Handle Ctrl+D or end of input stream
             logger.info("EOF detected, stopping input listener.")
             main_loop_is_running = False
             # Signal main loop to exit if it's waiting
             if main_loop:
                 # 使用 call_soon_threadsafe 直接设置事件，无需协程
                 main_loop.call_soon_threadsafe(new_input_event.set)
             break
        except Exception as e:
            if main_loop_is_running:
                 logger.error(f"Error in input listener: {e}", exc_info=True)
            else:
                 logger.info("Input listener stopping due to main loop exit.")
            break
    logger.info("Input listener thread finished.")

# --- Async Task Functions ---
async def stream_llm_to_tts(
    llm: LLMClient,
    messages: List[Dict[str, Any]],
    text_queue: asyncio.Queue,
    stop_event: asyncio.Event
):
    """Streams LLM response chunks to the TTS text queue."""
    logger.info("Starting LLM streaming task.")
    function_call_occurred = False
    try:
        async for chunk in llm.stream_chat(messages=messages):
            if stop_event.is_set():
                logger.info("LLM streaming task: Stop event detected, stopping stream.")
                break

            if chunk.get("error"):
                error_hint = chunk.get("hint", "Unknown LLM error")
                logger.error(f"LLM streaming error: {error_hint}")
                print(f"\nAssistant Error: {error_hint}")
                break # Stop processing on error

            content = chunk.get("content")
            tool_calls = chunk.get("tool_calls") # Check for function calls

            # Handle potential function calls (basic handling for now)
            if tool_calls:
                function_call_occurred = True
                logger.warning("Function call detected during streaming, stopping text-to-TTS.")
                print("\nAssistant: (Function call detected, response may be delayed or altered)")
                # In a real scenario, you'd handle the function call here.
                # For this test, we just stop sending text to TTS.
                break # Stop putting text in queue if function call happens

            if content:
                logger.debug(f"LLM stream received content: '{content[:50]}...'")
                try:
                    # Add to queue, wait slightly if full but check stop_event
                    while not stop_event.is_set():
                        try:
                            text_queue.put_nowait(content)
                            logger.debug(f"Put content chunk in text_queue (qsize: {text_queue.qsize()})")
                            break # Exit inner loop once put
                        except asyncio.QueueFull:
                            logger.debug("Text queue full, waiting briefly...")
                            await asyncio.sleep(0.05)
                except Exception as q_err:
                     logger.error(f"Error putting chunk in text_queue: {q_err}")
                     break # Stop if queue putting fails

            # Check finish reason if needed (e.g., 'stop', 'tool_calls')
            finish_reason = chunk.get("finish_reason")
            if finish_reason and finish_reason != 'null': # Check for non-null finish reason
                 logger.info(f"LLM stream finished with reason: {finish_reason}")
                 # No need to break here explicitly unless specific handling needed

        logger.info("LLM streaming task finished.")

    except LLMClientError as e:
        logger.error(f"LLMClientError during streaming: {e.hint}")
        print(f"\nAssistant Error: {e.hint}")
    except Exception as e:
        logger.error(f"Unexpected error during LLM streaming: {e}", exc_info=True)
        print(f"\nAssistant Error: An unexpected error occurred.")
    finally:
        # Always put None sentinel unless stopped prematurely by event or function call
        if not stop_event.is_set() and not function_call_occurred:
             try:
                  logger.info("Putting None sentinel in text_queue.")
                  # Wait briefly if queue is full, checking stop event
                  put_attempts = 0
                  max_attempts = 20 # ~1 second wait max
                  while put_attempts < max_attempts and not stop_event.is_set():
                    try:
                        text_queue.put_nowait(None)
                        logger.debug("None sentinel successfully placed.")
                        break
                    except asyncio.QueueFull:
                        put_attempts += 1
                        logger.debug(f"Text queue full when putting None, waiting ({put_attempts}/{max_attempts})...")
                        await asyncio.sleep(0.05)
                  if put_attempts == max_attempts:
                      logger.warning("Could not place None sentinel in text_queue after waiting.")

             except Exception as q_err:
                  logger.error(f"Error putting None sentinel in text_queue: {q_err}")
        elif function_call_occurred:
             logger.info("Function call occurred, not sending None sentinel.")
        else:
            logger.info("LLM streaming task was stopped, not sending None sentinel.")


async def stream_tts_and_play(
    tts: TTSClient,
    player: AudioNewPlayer,
    text_queue: asyncio.Queue,
    stop_event: asyncio.Event # Pass stop event for potential future use in TTS client
):
    """Gets text from queue, streams TTS, and plays audio."""
    logger.info("Starting TTS streaming and playback task.")
    audio_generator = None
    try:
        # Start TTS bidirectional stream
        logger.info("Calling tts.synthesize_stream_bidi...")
        audio_generator = tts.synthesize_stream_bidi(text_queue=text_queue)

        # Start audio playback
        logger.info("Calling player.play_stream...")
        # play_stream will run until audio finishes or it's stopped/cancelled
        await player.play_stream(
            audio_generator=audio_generator,
            sample_rate=TTS_SAMPLE_RATE,
            channels=TTS_CHANNELS
        )
        logger.info("player.play_stream finished.")

    except AudioPlayerError as e:
        # This might be triggered if player.stop() was called due to interruption
        if "播放错误" in str(e) or "停止" in str(e): # Basic check
             logger.info(f"Audio playback stopped or encountered an error (possibly due to interruption): {e}")
        else:
             logger.error(f"AudioPlayerError during playback: {e}", exc_info=True)
             print(f"\nPlayback Error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error during TTS/Playback: {e}", exc_info=True)
        print(f"\nAssistant Error: TTS or Playback failed.")
    finally:
        logger.info("TTS streaming and playback task finished.")
        # Note: AudioPlayer's play_stream should handle its own cleanup
        # including closing the TTS audio_generator implicitly if needed.
        # TTSClient's synthesize_stream_bidi also handles its cleanup.


async def handle_turn(
    user_input: str,
    llm: LLMClient,
    tts: TTSClient,
    player: AudioNewPlayer,
    text_queue: asyncio.Queue,
    shared_history: List[Dict[str, Any]], # Use shared history for context ONLY
    stop_event: asyncio.Event,
    tools: list
):
    logger.info(f"--- Starting Turn --- Input: '{user_input}'")
    # 移除这里的 "Assistant: Thinking..."，因为我们会流式打印
    # print(f"Assistant: Thinking...") # Initial feedback

    # 1. Clear the text queue (important for interruptions)
    while not text_queue.empty():
        try:
            text_queue.get_nowait()
        except asyncio.QueueEmpty:
            break
    logger.debug("Text queue cleared.")

    # 2. Prepare messages for the *initial* LLM call for *this turn*
    current_turn_messages = shared_history + [{"role": "user", "content": user_input}]

    # 3. Initial LLM stream, collect content for TTS and potential tool calls
    tool_calls_buffer = {}
    content_for_tts = ""
    finish_reason = None
    assistant_message_with_tool_calls = None
    tool_calls_final = []
    error_hint = None
    assistant_output_started = False # 标志位，判断是否已经开始打印 Assistant:

    try:
        logger.info(f"Initial LLM call with messages context (last 5): {json.dumps(current_turn_messages[-5:], ensure_ascii=False, indent=2)}") # Use indent for readability
        async for chunk in llm.stream_chat(messages=current_turn_messages, tools=tools):
            if stop_event.is_set():
                logger.info("LLM streaming interrupted by stop event.")
                break
            if chunk.get("error"):
                error_hint = chunk.get("hint", "Unknown LLM error")
                # 打印错误信息
                if not assistant_output_started:
                    print("Assistant: ", end='', flush=True)
                    assistant_output_started = True
                print(f"\n[LLM Error]: {error_hint}")
                logger.error(f"LLM error: {error_hint}")
                try:
                    text_queue.put_nowait(error_hint)
                    text_queue.put_nowait(None)
                    # Don't play the error here, let the main logic decide
                    # await stream_tts_and_play(tts, player, text_queue, stop_event)
                except Exception as tts_err: logger.error(f"Failed to queue LLM error for TTS: {tts_err}")
                return # Exit turn on LLM error

            # Collect content for potential direct TTS and print
            content = chunk.get("content")
            if content:
                # 仅在第一次收到内容时打印 "Assistant: "
                if not assistant_output_started:
                    print("Assistant: ", end='', flush=True)
                    assistant_output_started = True
                # 打印流式内容
                print(content, end='', flush=True)
                content_for_tts += content
                try: text_queue.put_nowait(content)
                except asyncio.QueueFull:
                    logger.warning("Text queue full during initial LLM stream, might lose content.")
                    await asyncio.sleep(0.01) # Brief yield

            # Accumulate tool calls
            tool_calls = chunk.get("tool_calls")
            if tool_calls:
                 # 如果有工具调用，可能需要换行以便清晰显示
                 if assistant_output_started:
                     print() # Add newline after potential text before tool call message
                     assistant_output_started = False # Reset for potential summary
                 logger.info(f"Tool call detected: {tool_calls}")
                 # 可以选择性打印提示信息
                 # print("(Calling tools...)")
                 for tc in tool_calls:
                    idx = tc.get("index")
                    if idx is None: continue
                    # Ensure deep copy to avoid modification issues if chunk object is reused
                    tc_copy = json.loads(json.dumps(tc))
                    if idx not in tool_calls_buffer:
                        tool_calls_buffer[idx] = tc_copy # Store the deep copy
                    else:
                        # Append arguments if the tool call is split across chunks
                        tool_calls_buffer[idx]["function"]["arguments"] += tc_copy["function"].get("arguments", "")


            # Check finish reason
            current_finish_reason = chunk.get("finish_reason")
            if current_finish_reason and current_finish_reason != 'null':
                finish_reason = current_finish_reason
                logger.info(f"LLM stream finished with reason: {finish_reason}")
                if finish_reason == "tool_calls":
                     if not tool_calls_buffer:
                         logger.error("Finish reason is 'tool_calls' but tool_calls_buffer is empty!")
                         # Handle as error or normal stop? Maybe treat as normal stop if no content?
                         finish_reason = "stop" # Fallback if no tool calls were actually parsed
                     else:
                         tool_calls_final = [tool_calls_buffer[k] for k in sorted(tool_calls_buffer.keys())]
                         assistant_message_with_tool_calls = {
                             "role": "assistant",
                             "content": None,
                             "tool_calls": tool_calls_final,
                         }
                         logger.info(f"Constructed assistant message with tool_calls: {json.dumps(assistant_message_with_tool_calls, ensure_ascii=False, indent=2)}")
                break # Stop processing stream chunks

        # 在 LLM 流结束后（如果不是因为错误或中断），打印一个换行符
        if assistant_output_started and not error_hint and not stop_event.is_set():
            print() # Newline after finishing streaming output

        if stop_event.is_set():
            logger.info("Turn interrupted during initial LLM stream.")
            print("\n[Interrupted]") # Indicate interruption
            return

    except Exception as e:
        logger.error(f"Error during initial LLM stream: {e}", exc_info=True)
        error_hint = f"An error occurred: {e}"
        print(f"\nAssistant Error: {error_hint}")
        try: # Try to queue the error for TTS
            text_queue.put_nowait(error_hint)
            text_queue.put_nowait(None)
        except Exception as tts_err: logger.error(f"Failed to queue stream error for TTS: {tts_err}")
        return # Exit turn

    # --- Decide action based on finish_reason ---
    final_assistant_reply_for_history = ""
    playback_task = None

    if finish_reason == "tool_calls" and assistant_message_with_tool_calls:
        # --- Function Call Path ---
        logger.info(f"Executing function call(s): {json.dumps(tool_calls_final, ensure_ascii=False, indent=2)}")
        print(f"[Executing Tools: {', '.join(tc['function']['name'] for tc in tool_calls_final)}]...") # Print tool names

        # Stop any TTS from potential initial content fragments before tool call
        await player.stop("Function call interrupt")
        while not text_queue.empty(): text_queue.get_nowait()

        # Execute local functions
        tool_msgs = []
        for call in tool_calls_final:
            function_name = call["function"]["name"]
            tool_call_id = call["id"]
            arguments_str = call["function"].get("arguments", "{}")
            try:
                # Use json.loads strict=False for potentially more lenient parsing if needed, but default is usually fine
                arguments = json.loads(arguments_str) if arguments_str else {}
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse JSON arguments for {function_name}: {arguments_str}. Error: {e}")
                tool_result = f"Tool call error: Invalid JSON arguments - {e}"
                arguments = None # Flag invalid arguments
                print(f"[Tool Error - {function_name}]: Invalid arguments: {arguments_str}")

            if arguments is not None:
                try:
                    logger.info(f"Calling function: {function_name} with args: {arguments}")
                    result = await FunctionRegistry.call(function_name, **arguments)
                    tool_result = str(result) if result is not None else "Tool executed successfully."
                    logger.info(f"Function {function_name} result: {tool_result}")
                    # Optional: Print tool result to console for debugging
                    print(f"[Tool Result - {function_name}]: {tool_result}")
                except Exception as e:
                    logger.error(f"Error executing function {function_name}: {e}", exc_info=True)
                    tool_result = f"Tool call error: {e}"
                    print(f"[Tool Error - {function_name}]: Execution failed: {e}")


            tool_msgs.append({
                "role": "tool",
                "tool_call_id": tool_call_id,
                "content": tool_result
            })

        messages_for_summary = current_turn_messages + [assistant_message_with_tool_calls] + tool_msgs
        logger.info(f"Messages for summary LLM call (last 5): {json.dumps(messages_for_summary[-5:], ensure_ascii=False, indent=2)}")

        # Call LLM for summary
        summary_content = ""
        summary_error_hint = None
        summary_output_started = False # Flag for summary output
        try:
            async for chunk in llm.stream_chat(messages=messages_for_summary, tools=tools): # Pass tools again if needed
                if stop_event.is_set():
                     logger.info("Summary LLM stream interrupted.")
                     break
                if chunk.get("error"):
                    summary_error_hint = chunk.get("hint", "Unknown summary error")
                    if not summary_output_started:
                         print("Assistant: ", end='', flush=True) # Print label if not started
                         summary_output_started = True
                    print(f"\n[LLM Summary Error]: {summary_error_hint}")
                    logger.error(f"LLM summary error: {summary_error_hint}")
                    summary_content = summary_error_hint # Use error as content
                    break
                content_piece = chunk.get("content")
                if content_piece:
                    if not summary_output_started:
                         print("Assistant: ", end='', flush=True) # Print label if not started
                         summary_output_started = True
                    print(content_piece, end='', flush=True) # Print summary content
                    summary_content += content_piece
                    try: text_queue.put_nowait(content_piece)
                    except asyncio.QueueFull:
                        logger.warning("Text queue full during summary stream.")
                        await asyncio.sleep(0.01)

            # Newline after summary stream if output started and no error/interrupt
            if summary_output_started and not summary_error_hint and not stop_event.is_set():
                print()

            if stop_event.is_set():
                 print("\n[Interrupted during summary]")
                 return # Don't proceed if interrupted

        except Exception as e:
            logger.error(f"Error during summary LLM stream: {e}", exc_info=True)
            summary_content = f"An error occurred during summary: {e}"
            print(f"\nAssistant Error (Summary): {summary_content}")
            try: text_queue.put_nowait(summary_content)
            except Exception: pass

        # Add sentinel for summary TTS
        if not stop_event.is_set():
            try: text_queue.put_nowait(None)
            except Exception: pass

        # Play the summary
        logger.info("Starting TTS playback for function call summary.")
        # TTS playback happens here, console output is already done.
        await stream_tts_and_play(tts, player, text_queue, stop_event)

        final_assistant_reply_for_history = summary_content if not summary_error_hint else "" # Store summary unless it was just an error message

    elif finish_reason == "stop" or content_for_tts: # Normal finish or partial content
        logger.info("Normal LLM reply or partial content generated.")
        if finish_reason == "stop" and not stop_event.is_set():
            try:
                text_queue.put_nowait(None)
                logger.debug("Added None sentinel for normal reply.")
            except Exception: pass
        elif stop_event.is_set():
             logger.info("Not adding sentinel as turn was interrupted.")

        # TTS playback happens here, console output is already done.
        if content_for_tts or finish_reason=="stop": # Play even if empty but finished normally
            logger.info("Starting TTS playback for normal/partial LLM response.")
            playback_task = asyncio.create_task(stream_tts_and_play(tts, player, text_queue, stop_event))
            try:
                await playback_task
                final_assistant_reply_for_history = content_for_tts if not stop_event.is_set() else ""
            except asyncio.CancelledError:
                 logger.info("Normal playback task cancelled.")
                 final_assistant_reply_for_history = ""
            except Exception as e:
                 logger.error(f"Error awaiting normal playback task: {e}")
                 final_assistant_reply_for_history = ""
        else:
             logger.info("No content generated and not a normal stop, skipping playback.")

    # 5. Update shared history
    shared_history.append({"role": "user", "content": user_input})
    if final_assistant_reply_for_history and not stop_event.is_set():
        logger.info("Appending final assistant text reply to shared history.")
        shared_history.append({"role": "assistant", "content": final_assistant_reply_for_history})
    else:
        logger.info("Not appending assistant reply to history (empty, interrupted, or error).")

    # Trim history (optional)
    max_history_turns = 10 # Keep last 10 turns (20 messages) - Increased slightly
    if len(shared_history) > max_history_turns * 2:
        logger.info(f"Trimming shared history from {len(shared_history)} items.")
        shared_history[:] = shared_history[-(max_history_turns * 2):]

    logger.info(f"--- Ending Turn --- Stop event set: {stop_event.is_set()}. Shared history length: {len(shared_history)}")


# --- Main Execution ---
async def main():
    global current_turn_stop_event, main_loop_is_running, main_loop
    
    # 获取并存储当前事件循环的引用
    main_loop = asyncio.get_running_loop()

    logger.info("Initializing components...")
    try:
        llm = LLMClient(llm_config_dict) # Pass the raw dictionary to LLMClient
        tts = TTSClient(tts_config_obj)
        
        # 1. 创建共享的 AudioNewPlayer 实例
        player = AudioNewPlayer() # Use default device
        
        # 2. 强制让 music_player_instance 使用这个共享的 player
        #    确保 music_player_instance 已经被导入
        music_player_instance.player = player
        logger.info("AudioNewPlayer instance shared with MusicPlayer.")

        tools = get_all_schemas() # <--- 新增
    except Exception as e:
        logger.error(f"Failed to initialize clients: {e}", exc_info=True)
        return

    text_queue = asyncio.Queue(maxsize=200) # Queue for LLM -> TTS text chunks
    shared_history: List[Dict[str, Any]] = []
    current_turn_task: Optional[asyncio.Task] = None

    # Start the input listener thread
    input_thread = threading.Thread(target=input_listener, name="InputListenerThread", daemon=True)
    input_thread.start()

    logger.info("Starting main interaction loop. Enter your message.")

    while main_loop_is_running:
        logger.info("Waiting for new input signal...")
        await new_input_event.wait()
        new_input_event.clear()
        logger.info("New input signal received.")

        if not main_loop_is_running:
             logger.info("Main loop received stop signal during wait.")
             break

        # Get the new input from the queue
        try:
            user_input = user_input_queue.get_nowait()
            logger.info(f"Processing new input: '{user_input}'")
        except queue.Empty:
            logger.warning("New input event was set, but queue is empty. Skipping turn.")
            continue # Should not happen with current logic, but safety first

        # If a previous turn is running, its stop event should have been set by the input thread.
        # We need to ensure its cleanup, especially player stop, completes.
        if current_turn_task and not current_turn_task.done():
            logger.info("Previous turn is still running, ensuring cleanup...")
            # Ensure the stop event is set (belt-and-suspenders)
            if current_turn_stop_event and not current_turn_stop_event.is_set():
                 logger.warning("Stop event was not set for previous turn? Setting it now.")
                 current_turn_stop_event.set()

            try:
                # Wait for the handle_turn task itself to finish its logic
                await asyncio.wait_for(current_turn_task, timeout=1.0) # Give it a bit more time
                logger.info("Previous handle_turn task finished or timed out.")
            except asyncio.TimeoutError:
                logger.warning("Timeout waiting for previous handle_turn task to finish after stop signal.")
                # Task might be stuck, try cancelling again more forcefully?
                current_turn_task.cancel()
            except asyncio.CancelledError:
                logger.info("Previous turn task was already cancelled.")
            except Exception as e:
                logger.error(f"Error awaiting previous turn task completion: {e}")

            # 显式确保播放器停止，并等待播放器变为IDLE状态
            if player.state != PlayerState.IDLE:
                logger.info(f"强制停止播放器 (当前状态: {player.state.name})...")
                try:
                    await player.stop("强制停止以处理新输入")
                except Exception as e:
                    logger.error(f"停止播放器时出错: {e}")

        # --- 新增：强制等待播放器彻底进入IDLE，确保打断式对话 ---
        max_wait = 5.0
        start_time = asyncio.get_event_loop().time()
        while player.state != PlayerState.IDLE:
            logger.info(f"等待播放器进入IDLE状态... 当前: {player.state.name}")
            await player.stop("确保彻底停止")
            await asyncio.sleep(0.1)
            if asyncio.get_event_loop().time() - start_time > max_wait:
                logger.error("播放器长时间未进入IDLE，强制跳过！")
                break
        logger.info(f"播放器现在处于状态: {player.state.name}")

        # Create a *new* stop event for *this* turn
        current_turn_stop_event = asyncio.Event()

        # Start the new turn handler task
        logger.info("Starting new handle_turn task.")
        current_turn_task = asyncio.create_task(
            handle_turn(user_input, llm, tts, player, text_queue, shared_history, current_turn_stop_event, tools),
            name=f"TurnHandler-{user_input[:10]}"
        )

    # --- Cleanup ---
    logger.info("Main loop exiting. Cleaning up...")
    main_loop_is_running = False # Signal input thread to stop

    # Cancel any final running turn task
    if current_turn_task and not current_turn_task.done():
        logger.info("Cancelling final turn task.")
        if current_turn_stop_event:
             current_turn_stop_event.set() # Ensure stop is signaled
        current_turn_task.cancel()
        try:
            await asyncio.wait_for(current_turn_task, timeout=1.0)
        except asyncio.TimeoutError:
            logger.warning("Timeout waiting for final turn task to cancel.")
        except asyncio.CancelledError:
             logger.info("Final turn task cancelled.")
        except Exception as e:
            logger.error(f"Error awaiting final turn task cancellation: {e}")

    # Close TTS client connection (important for websockets)
    try:
        logger.info("Closing TTS client...")
        await tts.close()
    except Exception as e:
        logger.error(f"Error closing TTS client: {e}")

    # Stop audio player (ensure resources released) - it should cleanup via handle_turn, but belt-and-suspenders
    try:
        logger.info("Ensuring audio player is stopped...")
        if player.state not in (PlayerState.IDLE, PlayerState.STOPPED, PlayerState.ERROR):
             await player.stop("Final cleanup")
    except Exception as e:
         logger.error(f"Error stopping audio player during final cleanup: {e}")

    # Wait for the input thread to finish (it's a daemon, but good practice)
    logger.info("Waiting for input thread to join...")
    input_thread.join(timeout=1.0)
    if input_thread.is_alive():
        logger.warning("Input thread did not join cleanly.")

    logger.info("Script finished.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("KeyboardInterrupt received, stopping.")
        main_loop_is_running = False # Signal threads/tasks to stop
        # Allow some cleanup time if needed
        # asyncio tasks might need a moment to react to cancellation flags
        # asyncio.get_event_loop().run_until_complete(asyncio.sleep(0.5))
    except Exception as e:
         logger.critical(f"Unhandled exception in main: {e}", exc_info=True)
