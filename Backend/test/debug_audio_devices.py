#!/usr/bin/env python3
"""
音频设备调试脚本
用于诊断Linux系统上的音频设备配置问题
"""

import subprocess
import os
import pygame

def run_command(cmd):
    """运行系统命令并返回输出"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.stdout, result.stderr, result.returncode
    except Exception as e:
        return "", str(e), -1

def check_alsa_devices():
    """检查ALSA设备"""
    print("=== ALSA 设备信息 ===")
    
    # 列出播放设备
    stdout, stderr, code = run_command("aplay -l")
    if code == 0:
        print("播放设备:")
        print(stdout)
    else:
        print(f"获取播放设备失败: {stderr}")
    
    # 列出录音设备
    stdout, stderr, code = run_command("arecord -l")
    if code == 0:
        print("录音设备:")
        print(stdout)
    else:
        print(f"获取录音设备失败: {stderr}")

def check_pulseaudio():
    """检查PulseAudio状态"""
    print("\n=== PulseAudio 信息 ===")
    
    # 检查PulseAudio是否运行
    stdout, stderr, code = run_command("pulseaudio --check -v")
    if code == 0:
        print("PulseAudio 正在运行")
    else:
        print("PulseAudio 未运行或有问题")
    
    # 列出PulseAudio输出设备
    stdout, stderr, code = run_command("pactl list short sinks")
    if code == 0:
        print("PulseAudio 输出设备:")
        print(stdout)
    else:
        print(f"获取PulseAudio设备失败: {stderr}")
    
    # 列出PulseAudio源设备
    stdout, stderr, code = run_command("pactl list short sources")
    if code == 0:
        print("PulseAudio 输入设备:")
        print(stdout)
    else:
        print(f"获取PulseAudio源设备失败: {stderr}")

def check_environment():
    """检查环境变量"""
    print("\n=== 环境变量 ===")
    audio_vars = ['SDL_AUDIODRIVER', 'PULSE_RUNTIME_PATH', 'XDG_RUNTIME_DIR']
    for var in audio_vars:
        value = os.environ.get(var, "未设置")
        print(f"{var}: {value}")

def test_pygame_devices():
    """测试pygame设备"""
    print("\n=== Pygame 设备测试 ===")
    
    # 设置环境变量
    os.environ['SDL_AUDIODRIVER'] = 'pulse'
    
    # 测试不同的设备名称
    device_names = [
        None,                    # 默认设备
        'UACDemoV1.0',          # 原始名称
        'UACDemoV10',           # 从aplay看到的名称
        'hw:0,0',               # ALSA硬件设备
        'plughw:0,0',           # ALSA插件设备
        'default',              # ALSA默认
        'pulse',                # PulseAudio
    ]
    
    for device_name in device_names:
        try:
            # 清理之前的初始化
            if pygame.mixer.get_init():
                pygame.mixer.quit()
            
            print(f"测试设备: {device_name if device_name else 'system_default'}")
            
            if device_name:
                pygame.mixer.init(devicename=device_name)
            else:
                pygame.mixer.init()
            
            # 获取初始化信息
            init_info = pygame.mixer.get_init()
            print(f"  ✓ 成功! 设置: {init_info}")
            
        except Exception as e:
            print(f"  ✗ 失败: {e}")
    
    # 清理
    if pygame.mixer.get_init():
        pygame.mixer.quit()

def test_simple_playback():
    """测试简单的音频播放"""
    print("\n=== 简单播放测试 ===")
    
    # 使用系统默认设备
    try:
        os.environ['SDL_AUDIODRIVER'] = 'pulse'
        pygame.mixer.init()
        
        # 创建一个简单的音调
        import numpy as np
        sample_rate = 22050
        duration = 1.0  # 1秒
        frequency = 440  # A4音调
        
        # 生成正弦波
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        wave = np.sin(frequency * 2 * np.pi * t)
        
        # 转换为pygame可用的格式
        wave = (wave * 32767).astype(np.int16)
        stereo_wave = np.array([wave, wave]).T
        
        # 创建Sound对象并播放
        sound = pygame.sndarray.make_sound(stereo_wave)
        print("播放测试音调...")
        sound.play()
        
        # 等待播放完成
        import time
        time.sleep(duration + 0.5)
        
        print("测试音调播放完成")
        
    except Exception as e:
        print(f"简单播放测试失败: {e}")
    finally:
        if pygame.mixer.get_init():
            pygame.mixer.quit()

def main():
    """主函数"""
    print("音频设备诊断工具")
    print("=" * 50)
    
    check_alsa_devices()
    check_pulseaudio()
    check_environment()
    test_pygame_devices()
    
    # 询问是否进行播放测试
    try:
        import numpy
        test_simple_playback()
    except ImportError:
        print("\n注意: 需要安装numpy来进行音频播放测试")
        print("运行: pip install numpy")

if __name__ == "__main__":
    main() 