import pygame
import os
import time

print("正在初始化 Pygame Mixer...")

# 强制使用 PulseAudio
os.environ['SDL_AUDIODRIVER'] = 'pulse'

# 函数：列出可用的音频设备
def list_audio_devices():
    """列出所有可用的音频设备"""
    print("尝试列出可用的音频设备...")
    try:
        # 初始化pygame mixer来获取设备信息
        pygame.mixer.pre_init()
        pygame.mixer.init()
        
        # 如果pygame支持get_init，显示当前设备信息
        if hasattr(pygame.mixer, 'get_init'):
            init_info = pygame.mixer.get_init()
            print(f"当前mixer设置: {init_info}")
        
        pygame.mixer.quit()
    except Exception as e:
        print(f"获取设备信息时出错: {e}")

# 函数：尝试不同的设备初始化方式
def try_initialize_mixer():
    """尝试不同的方式初始化mixer"""
    
    # 方法1: 尝试使用完整设备名
    # 注意: 如果想指定特定的PulseAudio设备，可以使用以下命令设置默认设备：
    # pactl set-default-sink alsa_output.usb-Jieli_Technology_UACDemoV1.0_5035988353193C1F-00.analog-stereo
    device_names = [
        'UACDemoV1.0',           # 原始尝试
        'UACDemoV10',            # 从aplay看到的名称
        'hw:0,0',                # ALSA硬件设备格式
        'plughw:0,0',            # ALSA插件设备格式
        'card0',                 # 简化的卡名
        None                     # 默认设备 (推荐使用)
    ]
    
    for device_name in device_names:
        try:
            print(f"尝试设备: {device_name if device_name else 'default'}")
            
            # 清理之前的初始化
            if pygame.mixer.get_init():
                pygame.mixer.quit()
            
            # 尝试初始化
            if device_name:
                pygame.mixer.init(devicename=device_name)
            else:
                pygame.mixer.init()
                
            print(f"✓ 成功使用设备: {device_name if device_name else 'default'}")
            return True
            
        except pygame.error as e:
            print(f"✗ 设备 {device_name if device_name else 'default'} 失败: {e}")
            continue
    
    print("所有设备尝试都失败了")
    return False

# 首先列出设备信息
list_audio_devices()

# 尝试初始化mixer
if not try_initialize_mixer():
    print("无法初始化任何音频设备，退出...")
    exit(1)

print("Mixer 初始化完成。")

# 音乐文件路径
music_file = "/home/<USER>/Desktop/eu03-asr-llm-tts/Backend/resource/彩虹.mp3"
print(f"准备加载音乐文件: {music_file}")

# 检查文件是否存在
if not os.path.exists(music_file):
    print(f"错误: 音乐文件不存在: {music_file}")
    exit(1)

# 加载音乐
try:
    pygame.mixer.music.load(music_file)
    print("音乐加载成功。")
except pygame.error as e:
    print(f"音乐加载失败: {e}")
    exit(1)

# 设置音量
pygame.mixer.music.set_volume(0.7)
print("音量设置为 0.7。")

# 播放音乐
print("开始播放...")
pygame.mixer.music.play()

# 等待音乐播放完成
while pygame.mixer.music.get_busy():
    time.sleep(0.1)

print("播放结束。")