import time
import logging
import sys
import os
import queue
from typing import List

# Adjust path to import from the parent directory
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from asr.asr_module import ASRModule
from utils.config_loader import load_config, ASRConfig

# --- Configuration ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'
)
logger = logging.getLogger("TEST_ASR")

# --- Test Callback ---
# Use a queue to safely pass results from the ASR thread to the main thread
asr_results_queue = queue.Queue()

def test_result_callback(text: str):
    """Callback function for the test."""
    logger.info(f"--- Test Callback Received: '{text}' ---")
    asr_results_queue.put(text)

# --- Main Test Function ---
def run_asr_test():
    """Runs the ASR module test indefinitely until interrupted."""
    asr_module: Optional[ASRModule] = None
    try:
        logger.info("Loading configuration...")
        config_loader = load_config()
        asr_config = config_loader.get_asr_config()
        logger.info(f"ASR Config Loaded. Model Dir: {asr_config.model_dir}, ALSA Device: {asr_config.alsa_device_name}")

        logger.info("Initializing ASRModule...")
        # Make sure ALSA is only used on Linux
        if sys.platform.startswith('linux'):
             if not asr_config.alsa_device_name:
                 logger.error("ALSA device name not found in config, but running on Linux.")
                 return False # Indicate test failure

             asr_module = ASRModule(config=asr_config, result_callback=test_result_callback)
             logger.info("ASRModule initialized.")
        else:
             logger.warning("Skipping ASR test: ALSA module requires Linux.")
             return True # Indicate test success (by skipping)

        logger.info("Starting ASRModule...")
        asr_module.start()
        # Give some time for threads to initialize and potentially fail if misconfigured
        time.sleep(2)
        # Check if the audio thread is alive (basic check)
        if asr_module._audio_thread and asr_module._audio_thread.is_alive():
             logger.info("ASR recording thread appears to be running.")
        else:
             logger.error("ASR recording thread did not start or died quickly. Check logs.")
             return False

        logger.info("ASR module running indefinitely. Press Ctrl+C to stop.")
        while True:
            # Keep the main thread alive, check thread health periodically
            if not (asr_module._audio_thread and asr_module._audio_thread.is_alive()):
                logger.error("ASR recording thread stopped unexpectedly!")
                return False # Indicate failure
            time.sleep(1) # Check every second

    except KeyboardInterrupt:
        logger.info("KeyboardInterrupt received. Stopping test...")
        return True # Indicate successful manual stop
    except FileNotFoundError as e:
        logger.error(f"Test Failed: Model file not found - {e}")
        return False
    except OSError as e:
         logger.error(f"Test Failed: OS Error (Likely ALSA issue or non-Linux platform) - {e}")
         return False
    except ValueError as e:
         logger.error(f"Test Failed: Value Error (Likely config issue) - {e}")
         return False
    except Exception as e:
        logger.error(f"Test Failed: An unexpected error occurred - {e}", exc_info=True)
        return False
    finally:
        if asr_module and asr_module._recording_active:
            logger.info("Stopping ASRModule...")
            asr_module.stop()
            logger.info("ASRModule stopped.")
        else:
             logger.info("ASRModule was not active or already stopped.")

        # Process any results received during the test
        received_results: List[str] = []
        while not asr_results_queue.empty():
            try:
                received_results.append(asr_results_queue.get_nowait())
            except queue.Empty:
                break
        logger.info(f"Total results received by callback during test run: {len(received_results)}")
        if received_results:
            logger.info(f"Final Results Queue: {received_results}")


if __name__ == "__main__":
    logger.info("========= Starting ASR Module Test (Continuous Run) =========")
    success = run_asr_test()
    if success:
        logger.info("========= ASR Module Test Finished =========")
        sys.exit(0)
    else:
        logger.error("========= ASR Module Test Failed =========")
        sys.exit(1)
