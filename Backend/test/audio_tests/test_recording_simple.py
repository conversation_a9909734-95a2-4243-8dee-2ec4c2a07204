#!/usr/bin/env python3
"""
简化的录音测试脚本
专门测试SoundDevice录音功能
"""

import sys
import numpy as np
import sounddevice as sd
import wave
from pathlib import Path

# 添加Backend目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

def find_ugreen_device():
    """查找UGREEN录音设备"""
    devices = sd.query_devices()
    
    print("=== 可用录音设备 ===")
    for i, device in enumerate(devices):
        if device['max_input_channels'] > 0:
            print(f"设备 {i}: {device['name']} (输入通道: {device['max_input_channels']})")
    
    # 查找UGREEN设备
    for i, device in enumerate(devices):
        if 'ugreen' in device['name'].lower() and device['max_input_channels'] > 0:
            print(f"\n✅ 找到UGREEN录音设备: {i} - {device['name']}")
            return i
    
    print("\n❌ 未找到UGREEN录音设备，使用默认设备")
    return None

def test_recording_with_sounddevice():
    """使用SoundDevice测试录音"""
    print("\n=== SoundDevice录音测试 ===")
    
    # 查找UGREEN设备
    device_id = find_ugreen_device()
    
    try:
        sample_rate = 48000  # 使用设备原生采样率
        channels = 1
        duration = 3
        
        print(f"\n🎤 开始录音 {duration} 秒...")
        print("   请对着麦克风说话")
        
        # 录音
        recording = sd.rec(
            int(duration * sample_rate), 
            samplerate=sample_rate, 
            channels=channels,
            device=device_id,
            dtype='int16'
        )
        sd.wait()  # 等待录音完成
        
        print("✅ 录音完成")
        
        # 分析录音数据
        audio_data = recording.flatten()
        max_amplitude = np.max(np.abs(audio_data))
        rms = np.sqrt(np.mean(audio_data.astype(np.float32) ** 2))
        
        print(f"📊 录音分析:")
        print(f"   最大振幅: {max_amplitude}")
        print(f"   RMS值: {rms:.2f}")
        print(f"   数据长度: {len(audio_data)} 样本")
        
        if max_amplitude > 1000:
            print("✅ 检测到强音频信号，麦克风工作正常")
        elif max_amplitude > 100:
            print("⚠️ 检测到微弱信号，可能音量太低")
        else:
            print("❌ 未检测到明显音频信号")
        
        # 保存录音文件
        output_file = "sounddevice_test.wav"
        with wave.open(output_file, 'wb') as wf:
            wf.setnchannels(channels)
            wf.setsampwidth(2)  # 16-bit
            wf.setframerate(sample_rate)
            wf.writeframes(recording.tobytes())
        
        print(f"💾 录音已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ SoundDevice录音测试失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🎤 简化录音测试开始...")
    print("=" * 40)
    
    # SoundDevice录音测试
    success = test_recording_with_sounddevice()
    
    if success:
        print("\n✅ 录音功能正常！")
        print("   UGREEN麦克风可以正常工作")
        print("   ASR服务应该能够正常录音")
    else:
        print("\n❌ 录音功能有问题")
        print("   请检查麦克风连接和设置")
    
    print("\n" + "=" * 40)
    print("🎤 测试完成")

if __name__ == "__main__":
    main()
