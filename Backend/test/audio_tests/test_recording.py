#!/usr/bin/env python3
"""
录音功能测试脚本
测试UGREEN麦克风的录音功能
"""

import asyncio
import logging
import subprocess
import sys
import os
import numpy as np
import sounddevice as sd
import wave
from pathlib import Path
import time

# 添加Backend目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(cmd):
    """运行系统命令并返回输出"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.stdout.strip(), result.stderr.strip(), result.returncode
    except Exception as e:
        return "", str(e), -1

def check_recording_devices():
    """检查录音设备"""
    print("=== 录音设备检查 ===")
    
    # 检查ALSA录音设备
    stdout, stderr, code = run_command("arecord -l")
    if code == 0:
        print("📋 ALSA录音设备:")
        print(stdout)
    else:
        print("❌ 无法列出ALSA录音设备")
    
    # 检查PulseAudio录音设备
    print("\n📋 PulseAudio录音设备:")
    stdout, stderr, code = run_command("pactl list short sources")
    if code == 0:
        for line in stdout.split('\n'):
            if line.strip() and 'monitor' not in line.lower():
                print(f"  - {line}")
    else:
        print("❌ 无法列出PulseAudio录音设备")
    
    # 检查SoundDevice录音设备
    print("\n📋 SoundDevice录音设备:")
    try:
        devices = sd.query_devices()
        for i, device in enumerate(devices):
            if device['max_input_channels'] > 0:
                print(f"  设备 {i}: {device['name']} (输入通道: {device['max_input_channels']})")
        
        # 查找UGREEN设备
        ugreen_devices = [i for i, dev in enumerate(devices) if 'ugreen' in dev['name'].lower()]
        if ugreen_devices:
            print(f"\n🎤 找到UGREEN设备: {ugreen_devices}")
            return ugreen_devices[0]
        else:
            print("❌ 未找到UGREEN设备")
            return None
            
    except Exception as e:
        print(f"❌ SoundDevice检查失败: {e}")
        return None

def test_simple_recording(device_id=None, duration=3):
    """测试简单录音"""
    print(f"\n=== 简单录音测试 ({duration}秒) ===")
    
    try:
        sample_rate = 16000  # ASR常用采样率
        channels = 1
        
        print(f"🎤 开始录音 {duration} 秒...")
        print("   请对着麦克风说话")
        
        # 录音
        recording = sd.rec(
            int(duration * sample_rate), 
            samplerate=sample_rate, 
            channels=channels,
            device=device_id,
            dtype='int16'
        )
        sd.wait()  # 等待录音完成
        
        print("✅ 录音完成")
        
        # 分析录音数据
        audio_data = recording.flatten()
        max_amplitude = np.max(np.abs(audio_data))
        rms = np.sqrt(np.mean(audio_data.astype(np.float32) ** 2))
        
        print(f"📊 录音分析:")
        print(f"   最大振幅: {max_amplitude}")
        print(f"   RMS值: {rms:.2f}")
        
        if max_amplitude > 1000:
            print("✅ 检测到音频信号，麦克风工作正常")
        elif max_amplitude > 100:
            print("⚠️ 检测到微弱信号，可能音量太低")
        else:
            print("❌ 未检测到明显音频信号，可能麦克风有问题")
        
        # 保存录音文件
        output_file = "test_recording.wav"
        with wave.open(output_file, 'wb') as wf:
            wf.setnchannels(channels)
            wf.setsampwidth(2)  # 16-bit
            wf.setframerate(sample_rate)
            wf.writeframes(recording.tobytes())
        
        print(f"💾 录音已保存到: {output_file}")
        
        return True, max_amplitude, rms
        
    except Exception as e:
        print(f"❌ 录音测试失败: {e}")
        return False, 0, 0

def test_continuous_recording(device_id=None, duration=10):
    """测试连续录音（模拟ASR场景）"""
    print(f"\n=== 连续录音测试 ({duration}秒) ===")
    
    try:
        sample_rate = 16000
        channels = 1
        chunk_duration = 0.5  # 每0.5秒处理一次
        chunk_size = int(sample_rate * chunk_duration)
        
        print(f"🎤 开始连续录音 {duration} 秒...")
        print("   模拟ASR实时处理场景")
        
        audio_chunks = []
        max_amplitudes = []
        
        def audio_callback(indata, frames, time, status):
            if status:
                print(f"录音状态: {status}")
            
            # 分析当前块
            chunk_data = indata[:, 0]  # 取第一个通道
            max_amp = np.max(np.abs(chunk_data))
            max_amplitudes.append(max_amp)
            audio_chunks.append(chunk_data.copy())
            
            # 实时显示音量
            volume_bar = "█" * int(max_amp / 1000)
            print(f"\r音量: {volume_bar:<20} {max_amp:>6.0f}", end="", flush=True)
        
        # 开始录音流
        with sd.InputStream(
            samplerate=sample_rate,
            channels=channels,
            device=device_id,
            callback=audio_callback,
            blocksize=chunk_size,
            dtype='float32'
        ):
            time.sleep(duration)
        
        print("\n✅ 连续录音完成")
        
        # 分析结果
        if max_amplitudes:
            avg_amplitude = np.mean(max_amplitudes)
            max_amplitude = np.max(max_amplitudes)
            
            print(f"📊 连续录音分析:")
            print(f"   平均振幅: {avg_amplitude:.2f}")
            print(f"   最大振幅: {max_amplitude:.2f}")
            print(f"   活跃块数: {sum(1 for amp in max_amplitudes if amp > 0.01)}/{len(max_amplitudes)}")
            
            if avg_amplitude > 0.01:
                print("✅ 连续录音正常，适合ASR使用")
            else:
                print("❌ 连续录音信号太弱")
        
        return True
        
    except Exception as e:
        print(f"❌ 连续录音测试失败: {e}")
        return False

def test_microphone_volume():
    """测试麦克风音量设置"""
    print("\n=== 麦克风音量检查 ===")
    
    # 检查ALSA麦克风音量
    stdout, stderr, code = run_command("amixer get Mic")
    if code == 0:
        print("🎚️ Mic音量设置:")
        print(stdout)
    else:
        # 尝试其他常见控制
        for control in ['Capture', 'Front Mic', 'Rear Mic']:
            stdout, stderr, code = run_command(f"amixer get '{control}'")
            if code == 0:
                print(f"🎚️ {control}音量设置:")
                print(stdout)
                break
    
    # 检查PulseAudio源音量
    stdout, stderr, code = run_command("pactl list sources | grep -A 10 'UGREEN'")
    if code == 0:
        print("\n🎚️ UGREEN设备状态:")
        print(stdout)

def suggest_recording_fixes():
    """提供录音修复建议"""
    print("\n=== 录音问题修复建议 ===")
    
    print("如果录音测试失败，请尝试以下步骤：")
    print()
    print("1. 检查麦克风音量:")
    print("   amixer set Capture 80%")
    print("   pactl set-source-volume @DEFAULT_SOURCE@ 80%")
    print()
    print("2. 检查麦克风是否静音:")
    print("   amixer set Capture unmute")
    print("   pactl set-source-mute @DEFAULT_SOURCE@ false")
    print()
    print("3. 设置UGREEN为默认录音设备:")
    print("   pactl set-default-source <UGREEN设备名称>")
    print()
    print("4. 检查物理连接:")
    print("   - 确认USB麦克风已正确连接")
    print("   - 检查麦克风开关/静音按钮")
    print("   - 尝试重新插拔USB连接")

def main():
    """主测试流程"""
    print("🎤 RDKX5录音功能测试开始...")
    print("=" * 50)
    
    # 检查录音设备
    ugreen_device = check_recording_devices()
    
    # 检查麦克风音量
    test_microphone_volume()
    
    # 简单录音测试
    success, max_amp, rms = test_simple_recording(ugreen_device, duration=3)
    
    if success and max_amp > 100:
        # 如果简单录音成功，进行连续录音测试
        test_continuous_recording(ugreen_device, duration=5)
    
    # 修复建议
    suggest_recording_fixes()
    
    print("\n" + "=" * 50)
    print("🎤 录音测试完成")

if __name__ == "__main__":
    main()
