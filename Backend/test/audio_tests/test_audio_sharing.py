#!/usr/bin/env python3
"""
测试音频设备共享功能
验证AudioNewPlayer和MusicPlayer能否同时使用同一个USB声卡设备
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path

# 添加Backend目录到Python路径
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from utils.audio_new_player import AudioNewPlayer
from utils.function_call.function_tools_impl import MusicPlayer
from utils.config_loader import load_config
import pygame

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_audio_sharing():
    """测试音频设备共享功能"""
    logger.info("开始测试音频设备共享功能...")
    
    try:
        # 1. 加载配置
        config_loader = load_config()
        audio_config = config_loader.get_audio_player_config()
        
        # 2. 创建AudioNewPlayer实例
        audio_player = AudioNewPlayer(
            device=audio_config.device,
            device_keyword=audio_config.device_keyword,
            blocksize=audio_config.blocksize,
            buffer_size=audio_config.buffer_size,
            min_prebuffer=audio_config.min_prebuffer,
            volume=audio_config.volume
        )
        logger.info(f"AudioNewPlayer创建成功，设备关键词: {audio_config.device_keyword}")
        
        # 3. 创建MusicPlayer实例并设置共享的AudioNewPlayer
        music_player = MusicPlayer()
        music_player.set_audio_player(audio_player)
        logger.info("MusicPlayer创建成功，已设置共享的AudioNewPlayer实例")
        
        # 4. 测试音乐播放器
        logger.info("测试音乐播放器...")
        music_list = music_player.list_music()
        if music_list:
            logger.info(f"找到音乐文件: {music_list}")
            # 播放第一首音乐
            result = music_player.play_music(music_list[0])
            logger.info(f"音乐播放结果: {result}")
            
            # 等待一段时间让音乐开始播放
            await asyncio.sleep(2)
            
            # 5. 测试TTS播放器（模拟）
            logger.info("测试TTS播放器（模拟音频流）...")
            
            # 创建一个简单的音频生成器（1秒的正弦波）
            async def generate_test_audio():
                import numpy as np
                sample_rate = 48000
                duration = 3  # 3秒
                frequency = 440  # A4音符
                
                # 生成正弦波
                t = np.linspace(0, duration, int(sample_rate * duration), False)
                wave = np.sin(2 * np.pi * frequency * t)
                
                # 转换为16位整数
                audio_data = (wave * 32767 * 0.3).astype(np.int16)  # 降低音量避免过响
                
                # 分块发送
                chunk_size = 1024
                for i in range(0, len(audio_data), chunk_size):
                    chunk = audio_data[i:i+chunk_size]
                    yield chunk.tobytes()
                    await asyncio.sleep(0.01)  # 模拟网络延迟
            
            # 播放测试音频
            try:
                await audio_player.play_stream(
                    audio_generator=generate_test_audio(),
                    sample_rate=48000,
                    channels=1
                )
                logger.info("TTS测试音频播放完成")
            except Exception as e:
                logger.error(f"TTS测试音频播放失败: {e}")
            
            # 6. 检查音乐是否还在播放
            if music_player.is_playing():
                logger.info("✅ 成功！音乐在TTS播放后仍在继续播放")
            else:
                logger.warning("⚠️  音乐在TTS播放后停止了")
            
            # 等待一段时间
            await asyncio.sleep(2)
            
            # 7. 停止音乐
            stop_result = music_player.stop_music()
            logger.info(f"停止音乐结果: {stop_result}")
            
        else:
            logger.warning("未找到音乐文件，跳过音乐播放测试")
            
            # 仅测试TTS播放器
            logger.info("仅测试TTS播放器...")
            
            async def generate_test_audio():
                import numpy as np
                sample_rate = 48000
                duration = 2
                frequency = 440
                
                t = np.linspace(0, duration, int(sample_rate * duration), False)
                wave = np.sin(2 * np.pi * frequency * t)
                audio_data = (wave * 32767 * 0.3).astype(np.int16)
                
                chunk_size = 1024
                for i in range(0, len(audio_data), chunk_size):
                    chunk = audio_data[i:i+chunk_size]
                    yield chunk.tobytes()
                    await asyncio.sleep(0.01)
            
            await audio_player.play_stream(
                audio_generator=generate_test_audio(),
                sample_rate=48000,
                channels=1
            )
            logger.info("TTS测试音频播放完成")
        
        logger.info("✅ 音频设备共享测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}", exc_info=True)

def test_device_detection():
    """测试音频设备检测"""
    logger.info("检测可用的音频设备...")
    
    try:
        import sounddevice as sd
        devices = sd.query_devices()
        
        logger.info("可用的音频设备:")
        for i, device in enumerate(devices):
            if device['max_output_channels'] > 0:
                logger.info(f"  {i}: {device['name']} (输出通道: {device['max_output_channels']})")
        
        # 检查是否有Yundea 1076设备
        yundea_devices = [i for i, d in enumerate(devices) 
                         if 'Y1076' in d['name'] or 'Yundea' in d['name']]
        if yundea_devices:
            logger.info(f"✅ 找到Yundea 1076设备: 索引 {yundea_devices}")
        else:
            logger.warning("⚠️  未找到Yundea 1076设备")
            
    except Exception as e:
        logger.error(f"设备检测失败: {e}")

if __name__ == "__main__":
    # 首先检测设备
    test_device_detection()
    
    # 然后测试音频共享
    asyncio.run(test_audio_sharing())
