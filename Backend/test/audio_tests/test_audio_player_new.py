import asyncio
import soundfile as sf
import numpy as np
import logging
from pathlib import Path
import sys
import sounddevice as sd
import time
from typing import Optional

# 添加项目根目录到 sys.path
# 获取当前脚本文件所在的目录
script_dir = Path(__file__).parent
# 获取项目根目录（假设脚本在 test 目录下）
project_root = script_dir.parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入被测试的类
from utils.audio_new_player import AudioNewPlayer, AudioPlayerError, PlayerState

# --- 配置 ---
LOG_LEVEL = logging.INFO # 可以设置为 logging.DEBUG 获取更详细日志
TARGET_DEVICE_INDEX = None # 使用默认设备，或者指定一个索引，例如 0
WAV_FILE_PATH = project_root / "test" / "resource" / "test_output_1.wav"
MP3_FILE_PATH = project_root / "test" / "resource" / "test.mp3"

# --- 日志设置 ---
logging.basicConfig(level=LOG_LEVEL,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("AudioNewPlayerTest")
# 如果需要，可以单独调整 AudioNewPlayer 内部的日志级别
logging.getLogger("utils.audio_new_player").setLevel(LOG_LEVEL)

# --- 辅助函数 ---
def load_audio_data(file_path: Path) -> Optional[tuple[bytes, int, int]]:
    """尝试加载音频文件，返回 (pcm_bytes, sample_rate, channels) 或 None"""
    if not file_path.exists():
        logger.error(f"测试文件不存在: {file_path}")
        return None
    try:
        logger.info(f"尝试使用 soundfile 加载: {file_path.name}")
        # 读取为 int16 NumPy 数组
        audio_data_np, sample_rate = sf.read(file_path, dtype='int16')
        channels = audio_data_np.shape[1] if audio_data_np.ndim > 1 else 1
        audio_bytes = audio_data_np.tobytes()
        logger.info(f"加载成功: {sample_rate} Hz, {channels} 声道, {len(audio_bytes)} bytes")
        return audio_bytes, sample_rate, channels
    except sf.LibsndfileError as e:
        logger.error(f"无法使用 soundfile 读取 '{file_path.name}': {e}. 可能是格式不支持或缺少解码库 (如 MP3 需要 ffmpeg 或 libsndfile 支持)。")
        return None
    except Exception as e:
        logger.error(f"加载音频文件 '{file_path.name}' 时发生意外错误: {e}", exc_info=True)
        return None

# --- 新增：正弦波生成器 ---
async def _sine_wave_stream_gen(sample_rate: int,
                                channels: int,
                                frequency: float,
                                chunk_frames: int,
                                duration: Optional[float] = None,
                                amplitude: float = 0.5):
    """异步生成正弦波音频块 (int16 PCM bytes)"""
    logger.info(f"开始生成正弦波流: {sample_rate} Hz, {channels}ch, {frequency} Hz, chunk={chunk_frames} frames, duration={duration or 'inf'}")
    t_start = 0.0
    sample_width = 2 # int16
    chunk_duration = chunk_frames / sample_rate
    total_frames_generated = 0
    max_frames = int(duration * sample_rate) if duration is not None else None

    try:
        while True:
            # 检查是否达到总时长
            if max_frames is not None and total_frames_generated >= max_frames:
                logger.info(f"正弦波生成达到设定时长 {duration} 秒，停止。")
                break

            # 计算当前块的帧数（最后一块可能不足 chunk_frames)
            frames_in_chunk = chunk_frames
            if max_frames is not None:
                 frames_in_chunk = min(chunk_frames, max_frames - total_frames_generated)

            if frames_in_chunk <= 0:
                 break # 如果计算出不需要帧了，也停止

            # 生成时间向量
            t = np.linspace(t_start, t_start + (frames_in_chunk - 1) / sample_rate, frames_in_chunk, endpoint=True)

            # 生成单声道正弦波数据
            sine_wave_mono = (amplitude * np.sin(2. * np.pi * frequency * t) * 32767).astype(np.int16)

            # 扩展到多声道（如果需要）
            if channels > 1:
                # shape: (frames_in_chunk,) -> (frames_in_chunk, 1) -> (frames_in_chunk, channels)
                sine_wave = np.repeat(sine_wave_mono[:, np.newaxis], channels, axis=1)
            else:
                sine_wave = sine_wave_mono

            # 转换为字节
            chunk_bytes = sine_wave.tobytes()
            yield chunk_bytes

            total_frames_generated += frames_in_chunk
            t_start += frames_in_chunk / sample_rate
            logger.debug(f"正弦波生成器: yielded {len(chunk_bytes)} bytes ({frames_in_chunk} frames), total frames: {total_frames_generated}")

            # 稍微等待，模拟处理时间或确保数据供应速率
            # 等待时间略小于块的实际时长，以保持缓冲区有数据
            await asyncio.sleep(chunk_duration * 0.8)

    except asyncio.CancelledError:
        logger.info("正弦波生成器被取消。")
        raise # 重新抛出以便外部任务知道
    except Exception as e:
        logger.error(f"正弦波生成器出错: {e}", exc_info=True)
    finally:
        logger.info(f"正弦波生成器结束。总生成帧数: {total_frames_generated}")


# --- 测试函数 ---

async def test_single_playback(player: AudioNewPlayer, file_path: Path):
    """测试播放单个完整音频文件"""
    logger.info(f"""
--- 测试: 单独播放 {file_path.name} ---""")
    audio_info = load_audio_data(file_path)
    if audio_info is None:
        logger.warning(f"跳过 {file_path.name} 的单独播放测试，因为无法加载。")
        return

    audio_bytes, sample_rate, channels = audio_info
    start_time = time.monotonic()
    try:
        await player.play(audio_bytes, sample_rate, channels)
        duration = time.monotonic() - start_time
        logger.info(f"单独播放 {file_path.name} 完成。耗时: {duration:.2f} 秒")
        assert player.state == PlayerState.STOPPED, f"播放结束后状态应为 STOPPED，但为 {player.state.name}"
    except AudioPlayerError as e:
        logger.error(f"单独播放 {file_path.name} 失败: {e}")
    except Exception as e:
        logger.error(f"单独播放 {file_path.name} 时发生意外错误: {e}", exc_info=True)


async def test_concurrent_playback(file_path1: Path, file_path2: Path, device_index: Optional[int]):
    """测试同时播放两个音频流（使用两个播放器实例）"""
    logger.info(f"""
--- 测试: 并发播放 {file_path1.name} 和 {file_path2.name} ---""")

    audio_info1 = load_audio_data(file_path1)
    audio_info2 = load_audio_data(file_path2)

    if audio_info1 is None or audio_info2 is None:
        logger.warning("跳过并发播放测试，因为一个或两个音频文件无法加载。")
        return

    audio_bytes1, sample_rate1, channels1 = audio_info1
    audio_bytes2, sample_rate2, channels2 = audio_info2

    player1 = AudioNewPlayer(device=device_index)
    player2 = AudioNewPlayer(device=device_index) # 尝试在同一设备上播放

    logger.info("启动两个播放任务...")
    start_time = time.monotonic()
    task1 = asyncio.create_task(player1.play(audio_bytes1, sample_rate1, channels1), name="Player1_Task")
    task2 = asyncio.create_task(player2.play(audio_bytes2, sample_rate2, channels2), name="Player2_Task")

    try:
        await asyncio.gather(task1, task2)
        duration = time.monotonic() - start_time
        logger.info(f"并发播放完成。耗时: {duration:.2f} 秒")
        assert player1.state == PlayerState.STOPPED, f"Player1 结束后状态应为 STOPPED，但为 {player1.state.name}"
        assert player2.state == PlayerState.STOPPED, f"Player2 结束后状态应为 STOPPED，但为 {player2.state.name}"
    except Exception as e:
        logger.error(f"并发播放期间发生错误: {e}", exc_info=True)
        # 尝试停止可能仍在运行的播放器
        await asyncio.gather(player1.stop("并发测试错误"), player2.stop("并发测试错误"), return_exceptions=True)


async def test_interruption(player: AudioNewPlayer, sample_rate: int = 24000, channels: int = 1, freq: float = 440):
    """测试在播放过程中停止 (使用正弦波生成器)"""
    logger.info(f"""--- 测试: 播放中停止 (正弦波 {freq} Hz) ---""")
    chunk_frames = player.blocksize # 使用播放器的块大小

    play_task = None
    try:
        logger.info(f"开始无限正弦波流播放 (将在 3 秒后停止)... SR={sample_rate}, Chan={channels}")
        # 创建无限持续时间的生成器 (duration=None)
        sine_gen = _sine_wave_stream_gen(sample_rate, channels, freq, chunk_frames, duration=None)

        play_task = asyncio.create_task(
            player.play_stream(sine_gen, sample_rate, channels),
            name="Interrupt_Test_Sine_Playback"
        )

        await asyncio.sleep(3.0) # 播放一段时间

        logger.info("请求停止播放...")
        stop_start_time = time.monotonic()
        await player.stop("测试中断(正弦波)")
        stop_duration = time.monotonic() - stop_start_time
        logger.info(f"stop() 方法调用完成，耗时: {stop_duration:.3f} 秒。等待播放任务结束...")

        # 等待 play_stream 任务结束 (它应该会因为 stop 而退出)
        await asyncio.wait_for(play_task, timeout=5.0) # 给足够时间清理
        logger.info("播放任务已结束。")

        assert player.state == PlayerState.STOPPED, f"停止后状态应为 STOPPED，但为 {player.state.name}"
        logger.info("正弦波播放中断测试成功。")

    except asyncio.TimeoutError:
        logger.error("停止测试失败：等待播放任务结束超时。")
        if play_task and not play_task.done():
             logger.info("尝试取消超时的播放任务...")
             play_task.cancel()
             try:
                 await play_task # 等待取消完成
             except asyncio.CancelledError:
                 logger.info("播放任务确认被取消。")
             except Exception as e_cancel:
                  logger.error(f"等待任务取消时出错: {e_cancel}")
    except AudioPlayerError as e:
        logger.error(f"停止测试播放失败: {e}")
    except Exception as e:
        logger.error(f"停止测试时发生意外错误: {e}", exc_info=True)
    finally:
        # 确保即使出错也尝试停止（理论上 play_stream 的 finally 会处理）
        if player.is_playing():
             logger.warning("中断测试结束时播放器仍在播放，强制停止。")
             await player.stop("中断测试结束强制停止")


async def test_streamed_playback(player: AudioNewPlayer, sample_rate: int = 24000, channels: int = 1, freq: float = 440, duration: float = 5.0):
    """测试通过异步生成器进行流式播放 (使用正弦波生成器)"""
    logger.info(f"""--- 测试: 流式播放 (正弦波 {freq} Hz, {duration}s) ---""")
    chunk_frames = player.blocksize # 使用播放器的块大小

    start_time = time.monotonic()
    try:
        # 使用正弦波流生成器
        sine_gen = _sine_wave_stream_gen(sample_rate, channels, freq, chunk_frames, duration=duration)
        await player.play_stream(sine_gen, sample_rate, channels)
        play_duration = time.monotonic() - start_time
        logger.info(f"正弦波流式播放完成。耗时: {play_duration:.2f} 秒")
        assert player.state == PlayerState.STOPPED, f"流式播放结束后状态应为 STOPPED，但为 {player.state.name}"
    except AudioPlayerError as e:
        logger.error(f"正弦波流式播放失败: {e}")
    except Exception as e:
        logger.error(f"正弦波流式播放时发生意外错误: {e}", exc_info=True)


async def main():
    """主测试流程"""
    logger.info("=" * 30)
    logger.info("开始 AudioNewPlayer 功能测试")
    logger.info("=" * 30)

    logger.info("""
可用音频设备:""")
    try:
        print(sd.query_devices())
        if TARGET_DEVICE_INDEX is not None:
             logger.info(f"""
将使用设备索引: {TARGET_DEVICE_INDEX}""")
             # 检查设备有效性
             sd.check_output_settings(device=TARGET_DEVICE_INDEX)
             logger.info(f"设备 {TARGET_DEVICE_INDEX} 检查通过。")
        else:
             logger.info("""
将使用默认输出设备。""")
    except Exception as e:
        logger.error(f"查询或检查音频设备时出错: {e}")
        logger.error("测试无法继续。请检查 sounddevice 安装和音频设备连接。")
        return

    # 创建主播放器实例
    player = AudioNewPlayer(device=TARGET_DEVICE_INDEX)

    # --- 定义测试参数 ---
    test_sample_rate = 24000
    test_channels = 1
    test_frequency = 440
    test_stream_duration = 5.0

    # --- 运行测试 ---
    # 1. 单独播放 WAV (保持)
    await test_single_playback(player, WAV_FILE_PATH)
    await asyncio.sleep(1)

    # 2. 单独播放 MP3 (保持, 如果支持)
    await test_single_playback(player, MP3_FILE_PATH)
    await asyncio.sleep(1)

    # 3. 流式播放 (使用正弦波)
    await test_streamed_playback(player,
                                 sample_rate=test_sample_rate,
                                 channels=test_channels,
                                 freq=test_frequency,
                                 duration=test_stream_duration)
    await asyncio.sleep(1)

    # 4. 播放中停止 (使用正弦波)
    await test_interruption(player,
                            sample_rate=test_sample_rate,
                            channels=test_channels,
                            freq=test_frequency)
    await asyncio.sleep(1)

    # 5. 并发播放 (保持文件播放)
    # 注意：并发播放在某些系统/驱动/设备上可能不稳定或失败
    # 如果遇到问题，可以注释掉此测试或确保音频设备支持多流
    await test_concurrent_playback(WAV_FILE_PATH, MP3_FILE_PATH, TARGET_DEVICE_INDEX)
    await asyncio.sleep(1)

    logger.info("\n" + "=" * 30)
    logger.info("AudioNewPlayer 测试结束")
    logger.info("=" * 30)
    # 新增：等待底层资源清理，减少事件循环关闭报错
    await asyncio.sleep(0.5)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试主程序运行出错: {e}", exc_info=True)
