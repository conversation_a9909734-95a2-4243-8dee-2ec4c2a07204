"""
形状检测系统控制模块

提供与形状检测系统API交互的功能调用接口
"""
import httpx
import logging
import asyncio
import socket
import json # 新增导入
from typing import Optional, Dict, Any, List, Tuple

# 导入配置加载器（用于获取服务地址和超时设置）
from utils.config_loader import load_config

logger = logging.getLogger("SHAPE_DETECTION_CONTROL")

# 全局变量，用于存储加载的配置
_shape_detection_config = None
# 最后一次连接失败的时间和原因
_last_failure: Tuple[float, str] = (0, "")

# 添加坐标验证函数，检查坐标是否在合理范围内
def _validate_distances(distance_x: float, distance_y: float) -> bool:
    """
    验证机械臂操作的距离参数是否在合理范围内
    
    Args:
        distance_x: X轴偏移距离
        distance_y: Y轴偏移距离
        
    Returns:
        bool: 距离值是否有效
    """
    # 检查是否为常见的固定值（如300, 0等），这通常表示LLM没有正确使用物体检测结果
    common_invalid_values = [0, 300, 320, 500, 100, 200]
    if distance_x in common_invalid_values and distance_y in common_invalid_values:
        logger.error(f"检测到可疑的距离值: X={distance_x}, Y={distance_y}，这可能是大模型使用了固定坐标而不是物体检测结果")
        return False
    
    # 检查距离范围是否合理（通常物体距离中心的偏移值应该在一个合理范围内）
    # 根据实际情况，这里假设有效范围为-400到400
    COORD_MIN, COORD_MAX = -400, 400
    if not (COORD_MIN <= distance_x <= COORD_MAX and COORD_MIN <= distance_y <= COORD_MAX):
        logger.error(f"距离值超出合理范围: X={distance_x}, Y={distance_y}，有效范围为{COORD_MIN}到{COORD_MAX}")
        return False
    
    return True

async def _get_shape_detection_config():
    """获取或加载形状检测配置"""
    global _shape_detection_config
    if _shape_detection_config is None:
        try:
            config_loader = load_config()
            # 获取形状检测系统配置
            shape_section = config_loader.get_config().get('intelligent_vision_arm_control', {})

            _shape_detection_config = {
                'base_url': shape_section.get('base_url', 'http://localhost:5000'),
                'timeout': shape_section.get('timeout', 5.0),
                'retries': shape_section.get('retries', 2)
            }

            if not _shape_detection_config['base_url']:
                logger.error("形状检测服务地址为空，请检查配置文件")
                raise ValueError("形状检测服务地址未配置")

            logger.info(f"已加载形状检测配置: {_shape_detection_config}")

            # 尝试解析主机名以验证配置是否有效
            try:
                url = _shape_detection_config['base_url']
                # 从URL中提取主机名
                if '://' in url:
                    host = url.split('://')[1].split(':')[0]
                else:
                    host = url.split(':')[0]

                if host != 'localhost' and not host.startswith('127.'):
                    # 尝试进行DNS解析，确认主机名/IP有效
                    socket.gethostbyname(host)
                    logger.info(f"已成功解析主机名: {host}")

            except socket.gaierror:
                logger.warning(f"无法解析主机名: {host}，请确认网络连接和主机名是否正确")
            except Exception as e:
                logger.warning(f"验证主机名时出错: {e}")

        except Exception as e:
            logger.error(f"加载形状检测配置失败: {e}", exc_info=True)
            # 提供默认配置以允许程序继续运行
            _shape_detection_config = {
                'base_url': 'http://************:5000',  # 使用估计的远程地址
                'timeout': 5.0,
                'retries': 2
            }
            logger.warning(f"使用默认形状检测配置: {_shape_detection_config}")

    return _shape_detection_config

async def _make_api_request(endpoint: str) -> Dict[str, Any]:
    """发送异步HTTP请求到形状检测API"""
    global _last_failure

    current_time = asyncio.get_event_loop().time()
    # 如果上次失败是在30秒内，且不是首次尝试，则返回缓存的错误
    if _last_failure[0] > 0 and current_time - _last_failure[0] < 30:
        logger.warning(f"上次请求失败后不到30秒 ({current_time - _last_failure[0]:.1f}s)，使用缓存的错误: {_last_failure[1]}")
        return {"success": False, "message": f"服务暂时不可用: {_last_failure[1]}"}

    try:
        config = await _get_shape_detection_config()
        url = f"{config['base_url'].rstrip('/')}{endpoint}"
        logger.info(f"请求形状检测API: {url}")

        # 设置超时和重试次数
        timeout = config['timeout']
        retries = config['retries']
        retry_count = 0

        while retry_count <= retries:
            try:
                logger.debug(f"尝试连接 ({retry_count+1}/{retries+1}): {url}")
                limits = httpx.Limits(max_keepalive_connections=5, max_connections=10, keepalive_expiry=30.0)
                async with httpx.AsyncClient(timeout=timeout, limits=limits) as client:
                    response = await client.get(url)
                    response.raise_for_status()
                    data = response.json()
                    logger.debug(f"API响应成功: {url}, 状态码: {response.status_code}")
                    # 成功后重置失败记录
                    _last_failure = (0, "")
                    return data
            except httpx.TimeoutException:
                retry_count += 1
                if retry_count <= retries:
                    logger.warning(f"请求超时，正在进行第 {retry_count}/{retries} 次重试...")
                    await asyncio.sleep(0.5)  # 在重试前短暂等待
                else:
                    logger.error(f"请求形状检测API超时，已重试 {retries} 次: {url}")
                    _last_failure = (current_time, "请求超时")
                    return {"success": False, "message": "请求形状检测服务超时"}
            except httpx.RequestError as e:
                retry_count += 1
                if retry_count <= retries:
                    logger.warning(f"请求错误，正在进行第 {retry_count}/{retries} 次重试: {e}")
                    await asyncio.sleep(0.5)
                else:
                    logger.error(f"请求形状检测API网络错误: {e}")
                    _last_failure = (current_time, f"连接错误: {e}")
                    return {"success": False, "message": f"连接形状检测服务失败: {e}"}
            except Exception as e:
                # 对于非网络错误，不进行重试
                logger.error(f"调用形状检测API出错: {e}", exc_info=True)
                _last_failure = (current_time, f"未知错误: {e}")
                return {"success": False, "message": f"调用形状检测服务失败: {e}"}
    except Exception as e:
        logger.error(f"准备请求时出错: {e}", exc_info=True)
        _last_failure = (current_time, f"初始化错误: {e}")
        return {"success": False, "message": f"初始化请求失败: {e}"}

async def shape_get_all_objects() -> str:
    """
    获取当前画面中检测到的所有物体信息，并以JSON字符串格式返回详细信息。
    """
    result = await _make_api_request('/api/measurements')

    # API调用失败处理
    if result.get("success") is False:
        return f"获取物体信息失败: {result.get('message', '未知错误')}"
    if "error" in result: # 兼容顶层error键
        return f"获取物体信息失败: {result.get('error', '未知错误')}"

    measurements = result.get('measurements', [])
    # 如果API响应中没有 'count' 键，则尝试使用 'measurements' 列表的长度
    # 如果 'measurements' 也不存在或为空，则 count 为 0
    count = result.get('count', len(measurements) if measurements else 0)


    # 无物体处理
    if count == 0 or not measurements:
        return '[]'  # 返回表示空列表的JSON字符串

    # 成功检测到物体处理
    objects_details = []
    for obj in measurements:
        position_data = obj.get('position', {})
        detail = {
            "id": obj.get('id'),
            "class_name": obj.get('class_name'), # 已在之前的迭代中修正
            "image_x": position_data.get('center_x'),
            "image_y": position_data.get('center_y'),
            "distance_x": position_data.get('distance_x'), # 新增
            "distance_y": position_data.get('distance_y')  # 新增
        }
        # 可选的 confidence
        if 'confidence' in obj:
            detail["confidence"] = obj.get('confidence')

        # 过滤掉关键信息不完整的物体
        if detail["id"] is not None and \
           detail["class_name"] is not None and \
           detail.get("image_x") is not None and \
           detail.get("image_y") is not None and \
           detail.get("distance_x") is not None and \
           detail.get("distance_y") is not None:
            objects_details.append(detail)
        else:
            logger.warning(f"物体信息不完整（可能缺少坐标、距离或类别信息），已跳过: {obj}")

    if not objects_details: # 如果过滤后列表为空
        return '[]'

    return json.dumps(objects_details)

async def shape_get_object_positions() -> str:
    """
    获取物体的位置信息，包括它们所在的象限
    """
    result = await _make_api_request('/api/measurements')

    if result.get("success") is False:
        return f"获取物体位置信息失败: {result.get('message', '未知错误')}"

    if "error" in result:
        return f"获取物体位置信息失败: {result.get('error', '未知错误')}"

    count = result.get('count', 0)
    if count == 0:
        return "当前画面中没有检测到物体。"

    measurements = result.get('measurements', [])

    # 定义象限描述
    quadrant_desc = {
        1: "右上",
        2: "左上",
        3: "左下",
        4: "右下"
    }

    # 按象限组织物体
    objects_by_quadrant = {}
    for obj in measurements:
        obj_id = obj.get('id', 'N/A')
        color = obj.get('class_name', '未知')
        position = obj.get('position', {})
        quadrant = position.get('quadrant', 0)

        # 获取象限描述，如果不在已知象限则显示坐标
        quadrant_name = quadrant_desc.get(quadrant, f"未知区域({position.get('center_x', 0)},{position.get('center_y', 0)})")

        if quadrant_name not in objects_by_quadrant:
            objects_by_quadrant[quadrant_name] = []

        objects_by_quadrant[quadrant_name].append(f"ID:{obj_id}的{color}物体")

    # 组装响应信息
    response_parts = [f"当前画面中{count}个物体的位置如下:"]

    for quadrant_name, objects in objects_by_quadrant.items():
        object_list = "、".join(objects)
        response_parts.append(f"{quadrant_name}象限: {object_list}")

    return "；".join(response_parts)

# 添加测试连接可用性的函数，用于手动检查
async def test_shape_detection_connection() -> str:
    """
    测试与形状检测系统的连接
    """
    config = await _get_shape_detection_config()
    url = f"{config['base_url'].rstrip('/')}/api/status"

    try:
        logger.info(f"测试连接到形状检测系统: {url}")
        async with httpx.AsyncClient(timeout=config['timeout']) as client:
            response = await client.get(url)
            response.raise_for_status()
            data = response.json()

            # 提取有用的状态信息
            fps = data.get('fps', 0)
            detection_count = data.get('detection_count', 0)

            message = f"连接成功! 形状检测系统状态: FPS={fps:.1f}, 当前检测到 {detection_count} 个物体"
            logger.info(message)
            return message
    except httpx.TimeoutException:
        error_msg = f"连接超时: {url}"
        logger.error(error_msg)
        return error_msg
    except httpx.RequestError as e:
        error_msg = f"连接错误: {e}"
        logger.error(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"测试连接时出错: {e}"
        logger.error(error_msg, exc_info=True)
        return error_msg
async def _make_api_post_request(endpoint: str, payload: Dict[str, Any]) -> Dict[str, Any]:
    """发送异步HTTP POST请求到智能视觉机械臂控制API"""
    global _last_failure

    current_time = asyncio.get_event_loop().time()
    # 如果上次失败是在30秒内，且不是首次尝试，则返回缓存的错误
    if _last_failure[0] > 0 and current_time - _last_failure[0] < 30: # 使用配置文件中的 cooldown
        logger.warning(f"上次请求失败后不到30秒 ({current_time - _last_failure[0]:.1f}s)，使用缓存的错误: {_last_failure[1]}")
        return {"status": "error", "message": f"服务暂时不可用: {_last_failure[1]}"}

    try:
        config = await _get_shape_detection_config() # 复用此函数获取 base_url, timeout, retries
        url = f"{config['base_url'].rstrip('/')}{endpoint}"
        logger.info(f"发送 POST 请求到 API: {url}，参数: {payload}")

        timeout = config['timeout']
        retries = config['retries']
        retry_count = 0

        while retry_count <= retries:
            try:
                logger.debug(f"尝试连接 ({retry_count+1}/{retries+1}): {url}")
                limits = httpx.Limits(max_keepalive_connections=5, max_connections=10, keepalive_expiry=30.0)
                async with httpx.AsyncClient(timeout=timeout, limits=limits) as client:
                    response = await client.post(url, json=payload)

                    if 200 <= response.status_code < 300:
                        logger.info(f"API POST 请求成功: {url}, 状态码: {response.status_code}")
                        _last_failure = (0, "") # 成功后重置失败记录
                        try:
                            return response.json() # 尝试解析JSON响应
                        except Exception: # pylint: disable=broad-except
                             # 如果响应不是JSON或为空，也视为成功
                            return {"status": "success", "message": "操作指令已发送，无详细响应内容。"}
                    else:
                        logger.error(f"API POST 请求失败: {url}, 状态码: {response.status_code}, 响应: {response.text}")
                        _last_failure = (current_time, f"API错误 {response.status_code}")
                        try:
                            error_data = response.json()
                            error_message = error_data.get("message", response.text)
                        except Exception: # pylint: disable=broad-except
                            error_message = response.text or "未能获取详细错误原因"
                        return {"status": "error", "message": f"机械臂操作失败: {error_message}"}

            except httpx.TimeoutException:
                retry_count += 1
                if retry_count <= retries:
                    logger.warning(f"POST 请求超时，正在进行第 {retry_count}/{retries} 次重试...")
                    await asyncio.sleep(0.5)
                else:
                    logger.error(f"POST 请求 API 超时，已重试 {retries} 次: {url}")
                    _last_failure = (current_time, "请求超时")
                    return {"status": "error", "message": "请求机械臂服务超时"}
            except httpx.RequestError as e:
                retry_count += 1
                if retry_count <= retries:
                    logger.warning(f"POST 请求错误，正在进行第 {retry_count}/{retries} 次重试: {e}")
                    await asyncio.sleep(0.5)
                else:
                    logger.error(f"POST 请求 API 网络错误: {e}")
                    _last_failure = (current_time, f"连接错误: {e}")
                    return {"status": "error", "message": f"连接机械臂服务失败: {e}"}
            except Exception as e: # pylint: disable=broad-except
                logger.error(f"调用 API POST 请求出错: {e}", exc_info=True)
                _last_failure = (current_time, f"未知错误: {e}")
                return {"status": "error", "message": f"调用机械臂服务失败: {e}"}
        # 如果循环结束仍未成功 (理论上不应到达这里，因为上面各种情况都有返回)
        _last_failure = (current_time, "重试次数耗尽")
        return {"status": "error", "message": "机械臂操作失败，已达最大重试次数"}

    except Exception as e: # pylint: disable=broad-except
        logger.error(f"准备 POST 请求时出错: {e}", exc_info=True)
        _last_failure = (current_time, f"初始化错误: {e}")
        return {"status": "error", "message": f"初始化机械臂请求失败: {e}"}

async def call_robot_arm_catch_object(distance_x: float, distance_y: float, height_z: float) -> Dict[str, str]:
    """
    控制机械臂移动到指定距离坐标并执行抓取动作。
    """
    # 验证距离是否合理
    if not _validate_distances(distance_x, distance_y):
        return {
            "status": "error", 
            "message": f"参数错误：提供的距离值(X={distance_x}, Y={distance_y})不是有效的物体偏移距离。请使用shape_get_all_objects返回的distance_x和distance_y值。"
        }
    
    endpoint = "/api/arm/catch_object"
    payload = {
        "image_x": distance_x,  # API仍然使用image_x，但我们的接口使用distance_x以保持与物体检测结果一致
        "image_y": distance_y,  # API仍然使用image_y，但我们的接口使用distance_y以保持与物体检测结果一致
        "height_z": height_z
    }
    logger.info(f"调用机械臂抓取: distance_x={distance_x}, distance_y={distance_y}, height_z={height_z}")
    result = await _make_api_post_request(endpoint, payload)

    if result.get("status") == "success":
        return {"status": "success", "message": result.get("message", "机械臂抓取指令已发送")}
    else:
        # _make_api_post_request 已经记录了详细日志
        return {"status": "error", "message": result.get("message", "机械臂抓取失败，未能获取详细原因。")}

async def call_robot_arm_put_object(distance_x: float, distance_y: float, height_z: float) -> Dict[str, str]:
    """
    控制机械臂移动到指定距离坐标并执行放置动作。
    """
    # 验证距离是否合理
    if not _validate_distances(distance_x, distance_y):
        return {
            "status": "error", 
            "message": f"参数错误：提供的距离值(X={distance_x}, Y={distance_y})不是有效的物体偏移距离。请使用shape_get_all_objects返回的distance_x和distance_y值。"
        }
    
    endpoint = "/api/arm/put_object"
    payload = {
        "image_x": distance_x,  # API仍然使用image_x，但我们的接口使用distance_x以保持与物体检测结果一致
        "image_y": distance_y,  # API仍然使用image_y，但我们的接口使用distance_y以保持与物体检测结果一致
        "height_z": height_z
    }
    logger.info(f"调用机械臂放置: distance_x={distance_x}, distance_y={distance_y}, height_z={height_z}")
    result = await _make_api_post_request(endpoint, payload)

    if result.get("status") == "success":
        return {"status": "success", "message": result.get("message", "机械臂放置指令已发送")}
    else:
        # _make_api_post_request 已经记录了详细日志
        return {"status": "error", "message": result.get("message", "机械臂放置失败，未能获取详细原因。")}

async def call_robot_arm_init_action() -> Dict[str, str]:
    """
    控制机械臂执行初始化动作，将机械臂从当前位置移动到初始位置。
    """
    endpoint = "/api/arm/init_action"
    payload = {}  # 初始化动作不需要参数
    logger.info("调用机械臂初始化动作")
    result = await _make_api_post_request(endpoint, payload)

    if result.get("status") == "success":
        return {"status": "success", "message": result.get("message", "机械臂初始化动作指令已发送")}
    else:
        return {"status": "error", "message": result.get("message", "机械臂初始化动作失败，未能获取详细原因。")}

async def call_robot_arm_normal_put_object(distance_x: float, distance_y: float, height_z: float) -> Dict[str, str]:
    """
    控制机械臂执行普通放置动作（非堆叠方式）。
    """
    # 验证距离是否合理
    if not _validate_distances(distance_x, distance_y):
        return {
            "status": "error", 
            "message": f"参数错误：提供的距离值(X={distance_x}, Y={distance_y})不是有效的物体偏移距离。请使用shape_get_all_objects返回的distance_x和distance_y值。"
        }
    
    endpoint = "/api/arm/normal_put_object"
    payload = {
        "image_x": distance_x,  # API仍然使用image_x，但我们的接口使用distance_x以保持与物体检测结果一致
        "image_y": distance_y,  # API仍然使用image_y，但我们的接口使用distance_y以保持与物体检测结果一致
        "height_z": height_z
    }
    logger.info(f"调用机械臂普通放置: distance_x={distance_x}, distance_y={distance_y}, height_z={height_z}")
    result = await _make_api_post_request(endpoint, payload)

    if result.get("status") == "success":
        return {"status": "success", "message": result.get("message", "机械臂普通放置指令已发送")}
    else:
        return {"status": "error", "message": result.get("message", "机械臂普通放置失败，未能获取详细原因。")}

async def call_robot_arm_stack_put_object(distance_x: float, distance_y: float, height_z: float) -> Dict[str, str]:
    """
    控制机械臂执行堆叠放置动作，适用于将物体放置在其他物体上方。
    """
    # 验证距离是否合理
    if not _validate_distances(distance_x, distance_y):
        return {
            "status": "error", 
            "message": f"参数错误：提供的距离值(X={distance_x}, Y={distance_y})不是有效的物体偏移距离。请使用shape_get_all_objects返回的distance_x和distance_y值。"
        }
    
    endpoint = "/api/arm/stack_put_object"
    payload = {
        "image_x": distance_x,  # API仍然使用image_x，但我们的接口使用distance_x以保持与物体检测结果一致
        "image_y": distance_y,  # API仍然使用image_y，但我们的接口使用distance_y以保持与物体检测结果一致
        "height_z": height_z
    }
    logger.info(f"调用机械臂堆叠放置: distance_x={distance_x}, distance_y={distance_y}, height_z={height_z}")
    result = await _make_api_post_request(endpoint, payload)

    if result.get("status") == "success":
        return {"status": "success", "message": result.get("message", "机械臂堆叠放置指令已发送")}
    else:
        return {"status": "error", "message": result.get("message", "机械臂堆叠放置失败，未能获取详细原因。")}
 
# --- 新增机械臂方向控制函数 ---

async def call_robot_arm_move_forward(distance: float) -> Dict[str, str]:
    """
    控制机械臂向前移动指定距离。
    
    Args:
        distance: 向前移动的距离，单位：厘米（必须为正数）
        
    Returns:
        Dict[str, str]: 操作结果状态和消息
    """
    if distance <= 0:
        return {
            "status": "error",
            "message": f"参数错误：移动距离必须为正数，当前值为{distance}"
        }
    
    endpoint = "/api/arm/move_forward"
    payload = {
        "distance": distance  # 距离单位：厘米
    }
    logger.info(f"调用机械臂向前移动: distance={distance}厘米")
    result = await _make_api_post_request(endpoint, payload)
    
    if result.get("status") == "success":
        return {"status": "success", "message": result.get("message", f"机械臂已向前移动{distance}厘米")}
    else:
        return {"status": "error", "message": result.get("message", "机械臂向前移动失败，未能获取详细原因。")}

async def call_robot_arm_move_back(distance: float) -> Dict[str, str]:
    """
    控制机械臂向后移动指定距离。
    
    Args:
        distance: 向后移动的距离，单位：厘米（必须为正数）
        
    Returns:
        Dict[str, str]: 操作结果状态和消息
    """
    if distance <= 0:
        return {
            "status": "error",
            "message": f"参数错误：移动距离必须为正数，当前值为{distance}"
        }
    
    endpoint = "/api/arm/move_back"
    payload = {
        "distance": distance  # 距离单位：厘米
    }
    logger.info(f"调用机械臂向后移动: distance={distance}厘米")
    result = await _make_api_post_request(endpoint, payload)
    
    if result.get("status") == "success":
        return {"status": "success", "message": result.get("message", f"机械臂已向后移动{distance}厘米")}
    else:
        return {"status": "error", "message": result.get("message", "机械臂向后移动失败，未能获取详细原因。")}

async def call_robot_arm_move_left(distance: float) -> Dict[str, str]:
    """
    控制机械臂向左移动指定距离。
    
    Args:
        distance: 向左移动的距离，单位：厘米（必须为正数）
        
    Returns:
        Dict[str, str]: 操作结果状态和消息
    """
    if distance <= 0:
        return {
            "status": "error",
            "message": f"参数错误：移动距离必须为正数，当前值为{distance}"
        }
    
    endpoint = "/api/arm/move_left"
    payload = {
        "distance": distance  # 距离单位：厘米
    }
    logger.info(f"调用机械臂向左移动: distance={distance}厘米")
    result = await _make_api_post_request(endpoint, payload)
    
    if result.get("status") == "success":
        return {"status": "success", "message": result.get("message", f"机械臂已向左移动{distance}厘米")}
    else:
        return {"status": "error", "message": result.get("message", "机械臂向左移动失败，未能获取详细原因。")}

async def call_robot_arm_move_right(distance: float) -> Dict[str, str]:
    """
    控制机械臂向右移动指定距离。
    
    Args:
        distance: 向右移动的距离，单位：厘米（必须为正数）
        
    Returns:
        Dict[str, str]: 操作结果状态和消息
    """
    if distance <= 0:
        return {
            "status": "error",
            "message": f"参数错误：移动距离必须为正数，当前值为{distance}"
        }
    
    endpoint = "/api/arm/move_right"
    payload = {
        "distance": distance  # 距离单位：厘米
    }
    logger.info(f"调用机械臂向右移动: distance={distance}厘米")
    result = await _make_api_post_request(endpoint, payload)
    
    if result.get("status") == "success":
        return {"status": "success", "message": result.get("message", f"机械臂已向右移动{distance}厘米")}
    else:
        return {"status": "error", "message": result.get("message", "机械臂向右移动失败，未能获取详细原因。")}

async def call_robot_arm_move_up(distance: float) -> Dict[str, str]:
    """
    控制机械臂向上移动指定距离。
    
    Args:
        distance: 向上移动的距离，单位：厘米（必须为正数）
        
    Returns:
        Dict[str, str]: 操作结果状态和消息
    """
    if distance <= 0:
        return {
            "status": "error",
            "message": f"参数错误：移动距离必须为正数，当前值为{distance}"
        }
    
    endpoint = "/api/arm/move_up"
    payload = {
        "distance": distance  # 距离单位：厘米
    }
    logger.info(f"调用机械臂向上移动: distance={distance}厘米")
    result = await _make_api_post_request(endpoint, payload)
    
    if result.get("status") == "success":
        return {"status": "success", "message": result.get("message", f"机械臂已向上移动{distance}厘米")}
    else:
        return {"status": "error", "message": result.get("message", "机械臂向上移动失败，未能获取详细原因。")}

async def call_robot_arm_move_down(distance: float) -> Dict[str, str]:
    """
    控制机械臂向下移动指定距离。
    
    Args:
        distance: 向下移动的距离，单位：厘米（必须为正数）
        
    Returns:
        Dict[str, str]: 操作结果状态和消息
    """
    if distance <= 0:
        return {
            "status": "error",
            "message": f"参数错误：移动距离必须为正数，当前值为{distance}"
        }
    
    endpoint = "/api/arm/move_down"
    payload = {
        "distance": distance  # 距离单位：厘米
    }
    logger.info(f"调用机械臂向下移动: distance={distance}厘米")
    result = await _make_api_post_request(endpoint, payload)
    
    if result.get("status") == "success":
        return {"status": "success", "message": result.get("message", f"机械臂已向下移动{distance}厘米")}
    else:
        return {"status": "error", "message": result.get("message", "机械臂向下移动失败，未能获取详细原因。")}