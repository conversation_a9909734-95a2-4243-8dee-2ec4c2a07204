"""
函数调用 schema 配置文件
"""

from .function_call_tools import FunctionRegistry

def get_calculator_schema():
    """计算器函数 schema"""
    return {
        "type": "function",
        "function": {
            "name": "calculator",
            "description": FunctionRegistry.get_description('calculator'),
            "parameters": {
                "type": "object",
                "properties": {
                    "op": {"type": "string", "enum": ["add", "sub", "mul", "div"], "description": "操作类型：加add、减sub、乘mul、除div"},
                    "a": {"type": "number", "description": "第一个数字"},
                    "b": {"type": "number", "description": "第二个数字"}
                },
                "required": ["op", "a", "b"]
            }
        }
    }

def get_music_player_schemas():
    """音乐播放器相关函数 schemas"""
    return [
        {
            "type": "function",
            "function": {
                "name": "music_player_list",
                "description": FunctionRegistry.get_description('music_player_list'),
                "parameters": {"type": "object", "properties": {}}
            }
        },
        {
            "type": "function",
            "function": {
                "name": "music_player_play_random",
                "description": FunctionRegistry.get_description('music_player_play_random'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "sample_rate": {"type": "number", "description": "采样率", "default": 48000}
                    }
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "music_player_play",
                "description": FunctionRegistry.get_description('music_player_play'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "music_name": {"type": "string", "description": "音乐文件名"},
                        "sample_rate": {"type": "number", "description": "采样率", "default": 48000}
                    },
                    "required": ["music_name"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "music_player_stop",
                "description": FunctionRegistry.get_description('music_player_stop'),
                "parameters": {"type": "object", "properties": {}}
            }
        },
        {
            "type": "function",
            "function": {
                "name": "music_player_increase_volume",
                "description": FunctionRegistry.get_description('music_player_increase_volume'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "step": {"type": "number", "description": "音量增加步长，默认0.1", "default": 0.1}
                    }
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "music_player_decrease_volume",
                "description": FunctionRegistry.get_description('music_player_decrease_volume'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "step": {"type": "number", "description": "音量减少步长，默认0.1", "default": 0.1}
                    }
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "music_player_set_volume",
                "description": FunctionRegistry.get_description('music_player_set_volume'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "value": {"type": "number", "description": "目标音量，范围0.0~1.0"}
                    },
                    "required": ["value"]
                }
            }
        }
    ]

# --- 新增 TTS 音量控制 Schema ---
def get_tts_player_schemas():
    """TTS播放器（语音助手）音量控制函数 schemas"""
    return [
        {
            "type": "function",
            "function": {
                "name": "tts_player_increase_volume",
                "description": FunctionRegistry.get_description('tts_player_increase_volume'), # 从注册处获取描述
                "parameters": {
                    "type": "object",
                    "properties": {
                        "step": {"type": "number", "description": "音量增加步长，默认0.1", "default": 0.1}
                    }
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "tts_player_decrease_volume",
                "description": FunctionRegistry.get_description('tts_player_decrease_volume'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "step": {"type": "number", "description": "音量减少步长，默认0.1", "default": 0.1}
                    }
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "tts_player_set_volume",
                "description": FunctionRegistry.get_description('tts_player_set_volume'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        # 保持与 music_player_set_volume 一致，让LLM更容易理解 value 的用法
                        "value": {"type": "string", "description": "目标音量：数字(如 0.8, 50)，百分比('50%'), 或描述性词语('最大','最小','一半','静音')"}
                    },
                    "required": ["value"]
                }
            }
        }
    ]

# --- 新增 Remote YOLO Demo 控制 Schema ---
def get_remote_yolo_schemas():
    """远程 YOLO Demo 控制函数 schemas"""
    return [
        {
            "type": "function",
            "function": {
                "name": "yolo_move_servo_relative",
                "description": FunctionRegistry.get_description('yolo_move_servo_relative'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "axis": {
                            "type": "string",
                            "enum": ["pan", "tilt"],
                            "description": "要移动的舵机轴：'pan' (左右) 或 'tilt' (上下)"
                        },
                        "delta": {
                            "type": "number",
                            "description": "要移动的角度变化量（度）。正数通常表示向右或向上，负数表示向左或向下。"
                        }
                    },
                    "required": ["axis", "delta"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "yolo_reset_servo",
                "description": FunctionRegistry.get_description('yolo_reset_servo'),
                "parameters": {"type": "object", "properties": {}} # 无参数
            }
        },
        {
            "type": "function",
            "function": {
                "name": "yolo_toggle_tracking",
                "description": FunctionRegistry.get_description('yolo_toggle_tracking'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "enabled": {
                            "type": "boolean",
                            "description": "设置为 true 以开启人脸追踪，设置为 false 以关闭。"
                        }
                    },
                    "required": ["enabled"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "yolo_get_face_count",
                "description": FunctionRegistry.get_description('yolo_get_face_count'),
                "parameters": {"type": "object", "properties": {}} # 无参数
            }
        }
    ]

# --- 新增 Shape Detection System Schema ---
def get_shape_detection_schemas():
    """获取形状检测系统相关的函数Schema"""
    return [
        {
            "type": "function",
            "function": {
                "name": "shape_get_all_objects",
                "description": FunctionRegistry.get_description("shape_get_all_objects"),
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "shape_get_object_positions",
                "description": FunctionRegistry.get_description("shape_get_object_positions"),
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "test_shape_detection_connection",
                "description": FunctionRegistry.get_description("test_shape_detection_connection"),
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        }
    ]

# --- 新增 Intelligent Vision Arm Control Schema ---
def get_robot_arm_control_schemas():
    """智能视觉机械臂控制函数 schemas"""
    return [
        {
            "type": "function",
            "function": {
                "name": "robot_arm_catch_object",
                "description": "控制机械臂移动到由视觉系统提供的物体位置并执行抓取动作。警告：参数distance_x和distance_y必须直接使用shape_get_all_objects返回的同名字段值，不能使用随机坐标或固定值。示例流程：1.先调用shape_get_all_objects获取物体信息，2.从结果中直接使用目标物体的distance_x和distance_y字段值作为参数传入。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "distance_x": {"type": "number", "description": "必须直接使用shape_get_all_objects返回结果中目标物体的distance_x值，表示物体中心相对于图像中心的X轴像素偏移距离。禁止使用随机值或固定值如300等。"},
                        "distance_y": {"type": "number", "description": "必须直接使用shape_get_all_objects返回结果中目标物体的distance_y值，表示物体中心相对于图像中心的Y轴像素偏移距离。禁止使用随机值或固定值如300等。"},
                        "height_z": {"type": "number", "description": "机械臂末端执行器进行操作的Z轴绝对高度，单位为毫米 (mm)。推荐值：抓取物体用50。"}
                    },
                    "required": ["distance_x", "distance_y", "height_z"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "robot_arm_put_object",
                "description": "控制机械臂将当前持有的物体移动到目标位置并执行放置动作。警告：参数distance_x和distance_y必须直接使用shape_get_all_objects返回的同名字段值，不能使用随机坐标或固定值。示例流程：1.先调用shape_get_all_objects获取物体信息，2.从结果中直接使用目标位置的distance_x和distance_y字段值作为参数传入。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "distance_x": {"type": "number", "description": "必须直接使用shape_get_all_objects返回结果中目标位置的distance_x值，表示目标位置相对于图像中心的X轴像素偏移距离。禁止使用随机值或固定值如300等。"},
                        "distance_y": {"type": "number", "description": "必须直接使用shape_get_all_objects返回结果中目标位置的distance_y值，表示目标位置相对于图像中心的Y轴像素偏移距离。禁止使用随机值或固定值如300等。"},
                        "height_z": {"type": "number", "description": "机械臂末端执行器进行操作的Z轴绝对高度，单位为毫米 (mm)。推荐值：普通放置用60。"}
                    },
                    "required": ["distance_x", "distance_y", "height_z"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "robot_arm_init_action",
                "description": "控制机械臂执行初始化动作，将机械臂从当前位置移动到初始位置。这个动作不需要任何参数，适合在开始新任务前或需要重置机械臂位置时使用。",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "robot_arm_normal_put_object",
                "description": "控制机械臂执行普通放置动作（非堆叠方式），将当前抓取的物体放置到指定位置。警告：参数distance_x和distance_y必须直接使用shape_get_all_objects返回的同名字段值，不能使用随机坐标或固定值。示例流程：1.先调用shape_get_all_objects获取物体信息，2.从结果中直接使用目标位置的distance_x和distance_y字段值作为参数传入。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "distance_x": {"type": "number", "description": "必须直接使用shape_get_all_objects返回结果中目标位置的distance_x值，表示目标位置相对于图像中心的X轴像素偏移距离。禁止使用随机值或固定值如300等。"},
                        "distance_y": {"type": "number", "description": "必须直接使用shape_get_all_objects返回结果中目标位置的distance_y值，表示目标位置相对于图像中心的Y轴像素偏移距离。禁止使用随机值或固定值如300等。"},
                        "height_z": {"type": "number", "description": "机械臂末端执行器进行操作的Z轴绝对高度，单位为毫米 (mm)。普通放置推荐值为60。"}
                    },
                    "required": ["distance_x", "distance_y", "height_z"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "robot_arm_stack_put_object",
                "description": "控制机械臂执行堆叠放置动作，将当前抓取的物体放置到另一个物体上方。警告：参数distance_x和distance_y必须直接使用shape_get_all_objects返回的同名字段值，不能使用随机坐标或固定值。示例流程：1.先调用shape_get_all_objects获取物体信息，2.从结果中直接使用目标位置的distance_x和distance_y字段值作为参数传入。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "distance_x": {"type": "number", "description": "必须直接使用shape_get_all_objects返回结果中目标位置的distance_x值，表示目标位置相对于图像中心的X轴像素偏移距离。禁止使用随机值或固定值如300等。"},
                        "distance_y": {"type": "number", "description": "必须直接使用shape_get_all_objects返回结果中目标位置的distance_y值，表示目标位置相对于图像中心的Y轴像素偏移距离。禁止使用随机值或固定值如300等。"},
                        "height_z": {"type": "number", "description": "机械臂末端执行器进行操作的Z轴绝对高度，单位为毫米 (mm)。堆叠放置推荐值为140。"}
                    },
                    "required": ["distance_x", "distance_y", "height_z"]
                }
            }
        },
        # --- 新增机械臂方向控制函数 schema ---
        {
            "type": "function",
            "function": {
                "name": "robot_arm_move_forward",
                "description": "控制机械臂向前移动指定距离，适用于需要微调机械臂位置的场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "distance": {"type": "number", "description": "向前移动的距离，单位为厘米（必须为正数）。推荐范围：1-5厘米。"}
                    },
                    "required": ["distance"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "robot_arm_move_back",
                "description": "控制机械臂向后移动指定距离，适用于需要微调机械臂位置的场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "distance": {"type": "number", "description": "向后移动的距离，单位为厘米（必须为正数）。推荐范围：1-5厘米。"}
                    },
                    "required": ["distance"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "robot_arm_move_left",
                "description": "控制机械臂向左移动指定距离，适用于需要微调机械臂位置的场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "distance": {"type": "number", "description": "向左移动的距离，单位为厘米（必须为正数）。推荐范围：1-5厘米。"}
                    },
                    "required": ["distance"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "robot_arm_move_right",
                "description": "控制机械臂向右移动指定距离，适用于需要微调机械臂位置的场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "distance": {"type": "number", "description": "向右移动的距离，单位为厘米（必须为正数）。推荐范围：1-5厘米。"}
                    },
                    "required": ["distance"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "robot_arm_move_up",
                "description": "控制机械臂向上移动指定距离，适用于需要微调机械臂高度的场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "distance": {"type": "number", "description": "向上移动的距离，单位为厘米（必须为正数）。推荐范围：1-5厘米。"}
                    },
                    "required": ["distance"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "robot_arm_move_down",
                "description": "控制机械臂向下移动指定距离，适用于需要微调机械臂高度的场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "distance": {"type": "number", "description": "向下移动的距离，单位为厘米（必须为正数）。推荐范围：1-5厘米。"}
                    },
                    "required": ["distance"]
                }
            }
        }
    ]

def get_all_schemas():
    """获取所有函数 schemas"""
    schemas = []
    schemas.append(get_calculator_schema())
    schemas.extend(get_music_player_schemas())
    schemas.extend(get_tts_player_schemas())
    schemas.extend(get_remote_yolo_schemas()) # 添加 Remote YOLO 控制 schemas
    # 注释掉机械臂相关的schemas，这些功能已迁移到MCP
    # schemas.extend(get_shape_detection_schemas()) # 已迁移到MCP
    # schemas.extend(get_robot_arm_control_schemas()) # 已迁移到MCP
    return schemas