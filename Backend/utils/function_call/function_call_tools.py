import os
from typing import List, Optional, Callable, Any, Dict, Awaitable
# from utils.audio_player import AudioPlayer, AudioPlayerError  # 已切换为 AudioNewPlayer，无需再导入
import inspect
import asyncio
from .function_tools_impl import Calculator, MusicPlayer
import re
from utils.audio_device_manager import audio_device_manager, AudioDeviceType
# 导入新的远程控制函数
from .remote_yolo_control import (
    yolo_move_servo_relative,
    yolo_reset_servo,
    yolo_toggle_tracking,
    yolo_get_face_count
)
# 导入形状检测系统控制函数 - 已迁移到MCP，注释掉旧的Function Calling
# from .vision_arm_control import ( # 修改为相对导入
#     shape_get_all_objects,
#     shape_get_object_positions,
#     test_shape_detection_connection,
#     call_robot_arm_catch_object,
#     call_robot_arm_put_object,
#     call_robot_arm_init_action,      # 新增导入
#     call_robot_arm_normal_put_object, # 新增导入
#     call_robot_arm_stack_put_object,  # 新增导入
#     call_robot_arm_move_forward,      # 新增导入方向控制函数
#     call_robot_arm_move_back,         # 新增导入方向控制函数
#     call_robot_arm_move_left,         # 新增导入方向控制函数
#     call_robot_arm_move_right,        # 新增导入方向控制函数
#     call_robot_arm_move_up,           # 新增导入方向控制函数
#     call_robot_arm_move_down          # 新增导入方向控制函数
# )

# 3. 自动注册与分发机制
class FunctionRegistry:
    _registry: Dict[str, Dict[str, Any]] = {}

    @classmethod
    def register_function(cls, name: str, description: str = ""):
        def decorator(func: Callable):
            cls._registry[name] = {
                'func': func,
                'description': description,
                'is_async': inspect.iscoroutinefunction(func)
            }
            return func
        return decorator

    @classmethod
    def get_function(cls, name: str):
        return cls._registry.get(name, {}).get('func')

    @classmethod
    def get_description(cls, name: str):
        return cls._registry.get(name, {}).get('description')

    @classmethod
    def is_async(cls, name: str):
        return cls._registry.get(name, {}).get('is_async', False)

    @classmethod
    async def call(cls, name: str, *args, **kwargs):
        entry = cls._registry.get(name)
        if not entry:
            raise ValueError(f'未注册的函数: {name}')
        func = entry['func']
        if entry['is_async']:
            return await func(*args, **kwargs)
        else:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: func(*args, **kwargs))

# 注册示例
@FunctionRegistry.register_function('calculator', '加减乘除计算器')
def calculator_func(op, a, b):
    try:
        result = Calculator.calculate(op, a, b)
        # 格式化结果，保留两位小数
        formatted_result = f"{result:.2f}"
        return f"计算结果：{formatted_result}"
    except ValueError as e:
        return f"计算错误：{str(e)}"
    except Exception as e:
        return f"计算过程出错：{str(e)}"

music_player_instance: Optional[MusicPlayer] = None

def initialize_music_player(player: MusicPlayer):
    """初始化并设置全局音乐播放器实例"""
    global music_player_instance
    music_player_instance = player

def set_shared_audio_player(player):
    """设置共享的AudioNewPlayer实例给音乐播放器"""
    if music_player_instance:
        music_player_instance.set_audio_player(player)

@FunctionRegistry.register_function('music_player_list', '获取本地可播放的背景音乐列表')
def music_player_list_func():
    result = music_player_instance.list_music()
    if not result:
        return "很抱歉，当前没有可播放的音乐。"
    return "可播放的歌曲有：" + "、".join([os.path.splitext(f)[0] for f in result])

@FunctionRegistry.register_function('music_player_play_random', '随机播放一首背景音乐')
def music_player_play_random_func(sample_rate=48000):
    return music_player_instance.play_random_music(sample_rate=sample_rate)

@FunctionRegistry.register_function('music_player_play', '播放指定的背景音乐')
def music_player_play_func(music_name, sample_rate=48000):
    return music_player_instance.play_music(music_name, sample_rate=sample_rate)

@FunctionRegistry.register_function('music_player_stop', '停止播放背景音乐')
def music_player_stop_func():
    return music_player_instance.stop_music()

@FunctionRegistry.register_function('music_player_increase_volume', '增加背景音乐播放器音量')
def music_player_increase_volume_func(step: float = 0.1):
    s = parse_relative_step(step)
    result_dict = music_player_instance.increase_volume(s)
    if result_dict["status"] == "success":
        return f"背景音乐音量已增加到{result_dict['volume']:.2f}"
    else:
        return f"设置失败: {result_dict['message']}"

@FunctionRegistry.register_function('music_player_decrease_volume', '减小背景音乐播放器音量')
def music_player_decrease_volume_func(step: float = 0.1):
    s = parse_relative_step(step)
    result_dict = music_player_instance.decrease_volume(s)
    if result_dict["status"] == "success":
        return f"背景音乐音量已减少到{result_dict['volume']:.2f}"
    else:
        return f"设置失败: {result_dict['message']}"

@FunctionRegistry.register_function('music_player_set_volume', '设置背景音乐播放器音量到指定值（0.0~1.0/最大/最小/百分比/一半/静音）')
def music_player_set_volume_func(value):
    v, vtype = parse_volume_value(value)
    result_dict = music_player_instance.set_volume(v)

    if result_dict["status"] == "success":
        volume = result_dict['volume']
        if vtype == 'mute':
            return "背景音乐已为您静音。"
        elif vtype == 'min':
            return "背景音乐音量已调到最小。"
        elif vtype == 'max':
            return "背景音乐音量已调到最大。"
        else:
            return f"背景音乐音量已设置为{volume:.2f}"
    else:
        return f"设置失败: {result_dict['message']}"

# --- TTS Player / System Volume Functions (AudioNewPlayer) ---

@FunctionRegistry.register_function('tts_player_set_volume', '设置语音助手（TTS）的音量到指定值（0.0~1.0/最大/最小/百分比/一半/静音）')
def tts_player_set_volume_func(value):
    if not music_player_instance.player:
        return "错误：TTS播放器未初始化，无法调节音量。"
    v, vtype = parse_volume_value(value)
    music_player_instance.player.set_volume(v)
    result = music_player_instance.player.get_volume() # 获取实际设置后的音量
    
    # 通知AudioDeviceManager记录TTS音量变化
    audio_device_manager.set_original_volume(AudioDeviceType.TTS_PLAYER, result)
    
    if vtype == 'mute':
        return "语音助手已为您静音。"
    elif vtype == 'min':
        return "语音助手音量已调到最小。"
    elif vtype == 'max':
        return "语音助手音量已调到最大。"
    else:
        return f"语音助手音量已设置为{result:.2f}"

@FunctionRegistry.register_function('tts_player_increase_volume', '增加语音助手（TTS）的音量')
def tts_player_increase_volume_func(step: float = 0.1):
    if not music_player_instance.player:
        return "错误：TTS播放器未初始化，无法调节音量。"
    s = parse_relative_step(step)
    current_volume = music_player_instance.player.get_volume()
    new_volume = min(1.0, current_volume + s)
    music_player_instance.player.set_volume(new_volume)
    result = music_player_instance.player.get_volume()
    
    # 通知AudioDeviceManager记录TTS音量变化
    audio_device_manager.set_original_volume(AudioDeviceType.TTS_PLAYER, result)
    
    return f"语音助手音量已增加到{result:.2f}"

@FunctionRegistry.register_function('tts_player_decrease_volume', '减小语音助手（TTS）的音量')
def tts_player_decrease_volume_func(step: float = 0.1):
    if not music_player_instance.player:
        return "错误：TTS播放器未初始化，无法调节音量。"
    s = parse_relative_step(step)
    current_volume = music_player_instance.player.get_volume()
    new_volume = max(0.0, current_volume - s)
    music_player_instance.player.set_volume(new_volume)
    result = music_player_instance.player.get_volume()
    
    # 通知AudioDeviceManager记录TTS音量变化
    audio_device_manager.set_original_volume(AudioDeviceType.TTS_PLAYER, result)
    
    return f"语音助手音量已减少到{result:.2f}"

# --- Remote YOLO Demo Control Functions ---

@FunctionRegistry.register_function('yolo_move_servo_relative', '控制远程摄像头云台相对转动指定的角度')
async def yolo_move_servo_relative_func(axis: str, delta: float):
    """包装函数，调用远程舵机相对移动实现"""
    return await yolo_move_servo_relative(axis, delta)

@FunctionRegistry.register_function('yolo_reset_servo', '让远程摄像头云台回到初始中间位置')
async def yolo_reset_servo_func():
    """包装函数，调用远程舵机回中实现"""
    return await yolo_reset_servo()

@FunctionRegistry.register_function('yolo_toggle_tracking', '开启或关闭远程摄像头的人脸自动追踪功能')
async def yolo_toggle_tracking_func(enabled: bool):
    """包装函数，调用远程切换追踪状态实现"""
    return await yolo_toggle_tracking(enabled)

@FunctionRegistry.register_function('yolo_get_face_count', '查询远程摄像头当前画面中是否有人脸以及有几张脸')
async def yolo_get_face_count_func():
    """包装函数，调用远程获取人脸数量实现"""
    return await yolo_get_face_count()

# --- Shape Detection System Functions - 已迁移到MCP ---
# 这些函数已迁移到MCP，LLM现在直接调用MCP工具：
# - mcp:vision-arm-control:get_measurements (替代 shape_get_all_objects)
# - mcp:vision-arm-control:get_system_status (替代 test_shape_detection_connection)

# @FunctionRegistry.register_function('shape_get_all_objects', "获取当前画面中检测到的所有物体的详细信息。返回一个JSON字符串，该字符串解析后是一个对象数组，每个对象代表一个检测到的物体。每个物体对象将包含以下键：'id' (物体的唯一标识符)、'class_name' (物体的类别名称)、'image_x' (物体中心的X轴像素坐标)、'image_y' (物体中心的Y轴像素坐标)、'distance_x' (物体中心点相对于图像中心的X轴像素偏移距离)、'distance_y' (物体中心点相对于图像中心的Y轴像素偏移距离)，以及可选的 'confidence' (检测置信度)。所有坐标和距离均为数字。如果未检测到有效物体，则返回表示空数组的JSON字符串 '[]'。")
# async def shape_get_all_objects_func() -> str:
#     """包装函数，调用形状检测获取所有物体信息实现"""
#     return await shape_get_all_objects()

# @FunctionRegistry.register_function('shape_get_object_positions', '获取当前画面中物体的位置信息，包括所在象限')
# async def shape_get_object_positions_func() -> str:
#     """包装函数，调用形状检测获取物体位置信息实现"""
#     return await shape_get_object_positions()

# @FunctionRegistry.register_function('test_shape_detection_connection', '测试形状检测系统连接')
# async def test_shape_detection_connection_func() -> str:
#     """包装函数，调用形状检测系统测试连接实现"""
#     return await test_shape_detection_connection()

# --- Intelligent Vision Arm Control Functions - 已迁移到MCP ---
# 这些函数已迁移到MCP，LLM现在直接调用MCP工具：
# - mcp:vision-arm-control:catch_object (替代 robot_arm_catch_object)
# - mcp:vision-arm-control:put_object (替代 robot_arm_put_object)

# @FunctionRegistry.register_function('robot_arm_catch_object', '控制机械臂抓取指定物体。参数 `distance_x` 和 `distance_y` 必须直接使用shape_get_all_objects函数返回的同名字段值。参数 `height_z` 表示物体在Z轴上的目标高度（单位：毫米）；如果用户未明确指定抓取高度，则必须使用默认值50。')
# async def robot_arm_catch_object_func(distance_x: float, distance_y: float, height_z: float) -> Dict[str, str]:
#     """包装函数，调用机械臂抓取物体实现"""
#     return await call_robot_arm_catch_object(distance_x, distance_y, height_z)

# @FunctionRegistry.register_function('robot_arm_put_object', '控制机械臂将当前抓取的物体放置到指定位置。参数 `distance_x` 和 `distance_y` 必须直接使用shape_get_all_objects函数返回的同名字段值（如果放置目标是视觉可见的物体）或由用户指定。参数 `height_z` 表示物体在Z轴上的目标放置高度（单位：毫米）；如果用户未明确指定放置高度，则必须使用默认值60。')
# async def robot_arm_put_object_func(distance_x: float, distance_y: float, height_z: float) -> Dict[str, str]:
#     """包装函数，调用机械臂放置物体实现"""
#     return await call_robot_arm_put_object(distance_x, distance_y, height_z)

# @FunctionRegistry.register_function('robot_arm_init_action', '控制机械臂执行初始化动作，将机械臂从当前位置移动到初始位置。这个动作不需要任何参数，适合在开始新任务前或需要重置机械臂位置时使用。')
# async def robot_arm_init_action_func() -> Dict[str, str]:
#     """包装函数，调用机械臂初始化动作实现"""
#     return await call_robot_arm_init_action()

# 注意：以下机械臂函数暂时保留注释，因为MCP服务器可能还没有实现所有这些功能
# 如果需要这些功能，可以在MCP服务器中添加相应的工具

# @FunctionRegistry.register_function('robot_arm_normal_put_object', '控制机械臂执行普通放置动作（非堆叠方式），将当前抓取的物体放置到指定位置。适用于将物体放置在平面上。参数 `distance_x` 和 `distance_y` 必须直接使用shape_get_all_objects函数返回的同名字段值（如果放置目标是视觉可见的物体）。`height_z` 是放置高度（单位：毫米）。对于普通放置，推荐的高度值为60毫米。')
# async def robot_arm_normal_put_object_func(distance_x: float, distance_y: float, height_z: float) -> Dict[str, str]:
#     """包装函数，调用机械臂普通放置实现"""
#     return await call_robot_arm_normal_put_object(distance_x, distance_y, height_z)

# @FunctionRegistry.register_function('robot_arm_stack_put_object', '控制机械臂执行堆叠放置动作，将当前抓取的物体放置到另一个物体上方。适用于物体堆叠场景。参数 `distance_x` 和 `distance_y` 必须直接使用shape_get_all_objects函数返回的同名字段值（如果放置目标是视觉可见的物体）。`height_z` 是放置高度（单位：毫米）。对于堆叠放置，推荐的高度值为140毫米。')
# async def robot_arm_stack_put_object_func(distance_x: float, distance_y: float, height_z: float) -> Dict[str, str]:
#     """包装函数，调用机械臂堆叠放置实现"""
#     return await call_robot_arm_stack_put_object(distance_x, distance_y, height_z)

# --- 新增机械臂方向控制函数注册 - 已迁移到MCP ---

# @FunctionRegistry.register_function('robot_arm_move_forward', '控制机械臂向前移动指定距离。参数 `distance` 表示要移动的距离，单位为厘米，必须为正数。')
# async def robot_arm_move_forward_func(distance: float) -> Dict[str, str]:
#     """包装函数，调用机械臂向前移动实现"""
#     return await call_robot_arm_move_forward(distance)

# @FunctionRegistry.register_function('robot_arm_move_back', '控制机械臂向后移动指定距离。参数 `distance` 表示要移动的距离，单位为厘米，必须为正数。')
# async def robot_arm_move_back_func(distance: float) -> Dict[str, str]:
#     """包装函数，调用机械臂向后移动实现"""
#     return await call_robot_arm_move_back(distance)

# @FunctionRegistry.register_function('robot_arm_move_left', '控制机械臂向左移动指定距离。参数 `distance` 表示要移动的距离，单位为厘米，必须为正数。')
# async def robot_arm_move_left_func(distance: float) -> Dict[str, str]:
#     """包装函数，调用机械臂向左移动实现"""
#     return await call_robot_arm_move_left(distance)

# @FunctionRegistry.register_function('robot_arm_move_right', '控制机械臂向右移动指定距离。参数 `distance` 表示要移动的距离，单位为厘米，必须为正数。')
# async def robot_arm_move_right_func(distance: float) -> Dict[str, str]:
#     """包装函数，调用机械臂向右移动实现"""
#     return await call_robot_arm_move_right(distance)

# @FunctionRegistry.register_function('robot_arm_move_up', '控制机械臂向上移动指定距离。参数 `distance` 表示要移动的距离，单位为厘米，必须为正数。')
# async def robot_arm_move_up_func(distance: float) -> Dict[str, str]:
#     """包装函数，调用机械臂向上移动实现"""
#     return await call_robot_arm_move_up(distance)

# @FunctionRegistry.register_function('robot_arm_move_down', '控制机械臂向下移动指定距离。参数 `distance` 表示要移动的距离，单位为厘米，必须为正数。')
# async def robot_arm_move_down_func(distance: float) -> Dict[str, str]:
#     """包装函数，调用机械臂向下移动实现"""
#     return await call_robot_arm_move_down(distance)

# --- Helper Functions ---

def parse_volume_value(value):
    if isinstance(value, (int, float)):
        v = float(value)
        if v == 0:
            return 0.0, 'mute'
        if 0 < v < 0.08:
            return 0.05, 'min'
        return max(0.0, min(1.0, v)), 'normal'
    if isinstance(value, str):
        v = value.strip()
        if v in ['静音', 'mute']:
            return 0.0, 'mute'
        if v in ['最小', '最小音量']:
            return 0.05, 'min'
        if v in ['最大', '最大音量', '全开', '最大声']:
            return 1.0, 'max'
        if v in ['一半', '一半音量', '50%', '百分之五十']:
            return 0.5, 'normal'
        m = re.match(r'([0-9]+)%', v)
        if m:
            percent = int(m.group(1))
            if percent == 0:
                return 0.0, 'mute'
            if 0 < percent < 8:
                return 0.05, 'min'
            if percent == 100:
                return 1.0, 'max'
            return max(0.0, min(1.0, percent / 100.0)), 'normal'
        m = re.match(r'百分之([0-9]+)', v)
        if m:
            percent = int(m.group(1))
            if percent == 0:
                return 0.0, 'mute'
            if 0 < percent < 8:
                return 0.05, 'min'
            if percent == 100:
                return 1.0, 'max'
            return max(0.0, min(1.0, percent / 100.0)), 'normal'
        try:
            f = float(v)
            if f == 0:
                return 0.0, 'mute'
            if 0 < f < 0.08:
                return 0.05, 'min'
            if 0 < f <= 1:
                return f, 'normal'
            if 1 < f <= 100:
                if f == 100:
                    return 1.0, 'max'
                if f < 8:
                    return 0.05, 'min'
                return f / 100.0, 'normal'
        except Exception:
            pass
    return 0.5, 'normal'

def parse_relative_step(step):
    if isinstance(step, str):
        if step in ['再大一点', '大一点', '加一点', '再高一点']:
            return 0.1
        if step in ['再小一点', '小一点', '减一点', '再低一点']:
            return 0.1
    try:
        return float(step)
    except Exception:
        return 0.1