#!/usr/bin/env python3
"""
MCP配置管理工具
允许用户方便地添加、删除、启用/禁用MCP服务器
"""

import json
import sys
import argparse
from pathlib import Path
from typing import Dict, Any

class MCPConfigManager:
    """MCP配置管理器"""
    
    def __init__(self, config_file: str = "mcp_servers.json"):
        # 如果是相对路径，则相对于当前目录
        if not Path(config_file).is_absolute():
            self.config_file = Path(__file__).parent / config_file
        else:
            self.config_file = Path(config_file)
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            return {"mcpServers": {}}
            
    def _save_config(self) -> None:
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
        print(f"✅ 配置已保存到 {self.config_file}")
        
    def list_servers(self) -> None:
        """列出所有MCP服务器"""
        servers = self.config.get("mcpServers", {})
        
        if not servers:
            print("📭 没有配置任何MCP服务器")
            return
            
        print(f"📋 MCP服务器列表 ({len(servers)} 个):")
        print("-" * 60)
        
        for name, config in servers.items():
            active = config.get("active", False)
            server_type = config.get("type", "unknown")
            url = config.get("url", config.get("command", "N/A"))
            description = config.get("description", "无描述")
            
            status = "🟢 活跃" if active else "🔴 非活跃"
            print(f"{status} {name}")
            print(f"   类型: {server_type}")
            print(f"   地址: {url}")
            print(f"   描述: {description}")
            print()
            
    def add_server(self, name: str, server_type: str, url: str = None, 
                   command: str = None, args: list = None, **kwargs) -> None:
        """添加MCP服务器"""
        if name in self.config["mcpServers"]:
            print(f"⚠️  服务器 '{name}' 已存在，使用 update 命令更新")
            return
            
        server_config = {
            "active": kwargs.get("active", True),
            "type": server_type,
            "description": kwargs.get("description", f"{name} MCP服务器")
        }
        
        if server_type in ["streamable-http", "sse"]:
            if not url:
                print("❌ HTTP/SSE类型服务器需要提供URL")
                return
            server_config.update({
                "url": url,
                "transport": server_type.replace("-", "_"),
                "timeout": kwargs.get("timeout", 30 if server_type == "streamable-http" else 5),
                "sse_read_timeout": kwargs.get("sse_read_timeout", 300),
                "headers": kwargs.get("headers", {})
            })
            if server_type == "streamable-http":
                server_config["terminate_on_close"] = kwargs.get("terminate_on_close", True)
                
        elif server_type == "stdio":
            if not command:
                print("❌ stdio类型服务器需要提供command")
                return
            server_config.update({
                "command": command,
                "args": args or [],
                "cwd": kwargs.get("cwd", "."),
                "env": kwargs.get("env", {})
            })
        else:
            print(f"❌ 不支持的服务器类型: {server_type}")
            return
            
        self.config["mcpServers"][name] = server_config
        self._save_config()
        print(f"✅ 已添加MCP服务器: {name}")
        
    def remove_server(self, name: str) -> None:
        """删除MCP服务器"""
        if name not in self.config["mcpServers"]:
            print(f"❌ 服务器 '{name}' 不存在")
            return
            
        del self.config["mcpServers"][name]
        self._save_config()
        print(f"✅ 已删除MCP服务器: {name}")
        
    def enable_server(self, name: str) -> None:
        """启用MCP服务器"""
        if name not in self.config["mcpServers"]:
            print(f"❌ 服务器 '{name}' 不存在")
            return
            
        self.config["mcpServers"][name]["active"] = True
        self._save_config()
        print(f"✅ 已启用MCP服务器: {name}")
        
    def disable_server(self, name: str) -> None:
        """禁用MCP服务器"""
        if name not in self.config["mcpServers"]:
            print(f"❌ 服务器 '{name}' 不存在")
            return
            
        self.config["mcpServers"][name]["active"] = False
        self._save_config()
        print(f"✅ 已禁用MCP服务器: {name}")
        
    def update_server(self, name: str, **kwargs) -> None:
        """更新MCP服务器配置"""
        if name not in self.config["mcpServers"]:
            print(f"❌ 服务器 '{name}' 不存在")
            return
            
        server_config = self.config["mcpServers"][name]
        
        # 更新配置
        for key, value in kwargs.items():
            if value is not None:
                if key == "headers" and isinstance(value, str):
                    # 解析headers字符串
                    try:
                        value = json.loads(value)
                    except json.JSONDecodeError:
                        print(f"❌ headers格式错误: {value}")
                        return
                server_config[key] = value
                
        self._save_config()
        print(f"✅ 已更新MCP服务器: {name}")

def main():
    parser = argparse.ArgumentParser(description="MCP配置管理工具")
    parser.add_argument("--config", default="mcp_servers.json", help="配置文件路径")
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # list命令
    subparsers.add_parser("list", help="列出所有MCP服务器")
    
    # add命令
    add_parser = subparsers.add_parser("add", help="添加MCP服务器")
    add_parser.add_argument("name", help="服务器名称")
    add_parser.add_argument("type", choices=["streamable-http", "sse", "stdio"], help="服务器类型")
    add_parser.add_argument("--url", help="服务器URL (HTTP/SSE类型)")
    add_parser.add_argument("--command", help="命令 (stdio类型)")
    add_parser.add_argument("--args", nargs="*", help="命令参数 (stdio类型)")
    add_parser.add_argument("--description", help="服务器描述")
    add_parser.add_argument("--timeout", type=int, help="连接超时时间")
    add_parser.add_argument("--headers", help="HTTP头部 (JSON格式)")
    add_parser.add_argument("--inactive", action="store_true", help="添加为非活跃状态")
    
    # remove命令
    remove_parser = subparsers.add_parser("remove", help="删除MCP服务器")
    remove_parser.add_argument("name", help="服务器名称")
    
    # enable/disable命令
    enable_parser = subparsers.add_parser("enable", help="启用MCP服务器")
    enable_parser.add_argument("name", help="服务器名称")
    
    disable_parser = subparsers.add_parser("disable", help="禁用MCP服务器")
    disable_parser.add_argument("name", help="服务器名称")
    
    # update命令
    update_parser = subparsers.add_parser("update", help="更新MCP服务器配置")
    update_parser.add_argument("name", help="服务器名称")
    update_parser.add_argument("--url", help="更新URL")
    update_parser.add_argument("--description", help="更新描述")
    update_parser.add_argument("--timeout", type=int, help="更新超时时间")
    update_parser.add_argument("--headers", help="更新HTTP头部 (JSON格式)")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
        
    manager = MCPConfigManager(args.config)
    
    if args.command == "list":
        manager.list_servers()
    elif args.command == "add":
        headers = {}
        if args.headers:
            try:
                headers = json.loads(args.headers)
            except json.JSONDecodeError:
                print(f"❌ headers格式错误: {args.headers}")
                return
                
        manager.add_server(
            args.name, args.type, args.url, args.command, args.args,
            description=args.description,
            timeout=args.timeout,
            headers=headers,
            active=not args.inactive
        )
    elif args.command == "remove":
        manager.remove_server(args.name)
    elif args.command == "enable":
        manager.enable_server(args.name)
    elif args.command == "disable":
        manager.disable_server(args.name)
    elif args.command == "update":
        kwargs = {}
        if args.url: kwargs["url"] = args.url
        if args.description: kwargs["description"] = args.description
        if args.timeout: kwargs["timeout"] = args.timeout
        if args.headers: kwargs["headers"] = args.headers
        manager.update_server(args.name, **kwargs)

if __name__ == "__main__":
    main()
