"""
MCP集成模块
将MCP客户端与现有Function Calling系统集成
提供统一的工具调用接口，支持本地工具和MCP工具的混合使用
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass

# 项目内部导入
from .mcp_client_manager import MCPClientManager, MCPTool
from .mcp_environment import get_environment_info

# 安全导入Function Calling模块（在WSL2等环境中可能因音频库问题失败）
try:
    from ...function_call.function_call_tools import FunctionRegistry
    FUNCTION_REGISTRY_AVAILABLE = True
except (ImportError, OSError) as e:
    logging.warning(f"Function Calling模块导入失败（可能是音频库问题）: {e}")
    # 创建一个最小的占位符
    class FunctionRegistry:
        _registry = {}
        @classmethod
        async def call(cls, name, **kwargs):
            raise RuntimeError("Function Calling功能在当前环境中不可用")
    FUNCTION_REGISTRY_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class IntegratedTool:
    """集成工具描述 - 统一本地工具和MCP工具"""
    name: str
    description: str
    parameters: Dict[str, Any]
    origin: str  # "local" 或 "mcp"
    server_name: Optional[str] = None  # MCP工具的服务器名称
    active: bool = True
    
    def to_openai_format(self) -> Dict[str, Any]:
        """转换为OpenAI Function Calling格式"""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.parameters
            }
        }
        
    def to_anthropic_format(self) -> Dict[str, Any]:
        """转换为Anthropic格式"""
        return {
            "name": self.name,
            "description": self.description,
            "input_schema": {
                "type": "object",
                "properties": self.parameters.get("properties", {}),
                "required": self.parameters.get("required", [])
            }
        }

class MCPFunctionCallIntegrator:
    """MCP与Function Calling集成器"""
    
    def __init__(self):
        self.mcp_manager: Optional[MCPClientManager] = None
        self.integrated_tools: List[IntegratedTool] = []
        self.env_info = get_environment_info()
        self._initialized = False
        
    async def initialize(self) -> None:
        """初始化集成器"""
        if self._initialized:
            return
            
        logger.info("初始化MCP Function Calling集成器...")
        
        # 初始化MCP客户端管理器
        self.mcp_manager = MCPClientManager()
        await self.mcp_manager.initialize()
        
        # 加载本地工具
        self._load_local_tools()
        
        # 加载MCP工具
        await self._load_mcp_tools()
        
        self._initialized = True
        logger.info("MCP Function Calling集成器初始化完成")
        
    def _load_local_tools(self) -> None:
        """加载本地Function Calling工具"""
        logger.info("加载本地Function Calling工具...")

        if not FUNCTION_REGISTRY_AVAILABLE:
            logger.warning("Function Registry不可用，跳过本地工具加载")
            return

        # 从FunctionRegistry获取已注册的工具
        for name, entry in FunctionRegistry._registry.items():
            # 构造参数schema（简化版本）
            parameters = {
                "type": "object",
                "properties": {},
                "required": []
            }

            tool = IntegratedTool(
                name=name,
                description=entry.get('description', ''),
                parameters=parameters,
                origin="local"
            )
            self.integrated_tools.append(tool)

        logger.info(f"加载了 {len([t for t in self.integrated_tools if t.origin == 'local'])} 个本地工具")
        
    async def _load_mcp_tools(self) -> None:
        """加载MCP工具"""
        if not self.mcp_manager:
            return
            
        logger.info("加载MCP工具...")
        
        mcp_tools = self.mcp_manager.get_available_tools()
        
        for mcp_tool in mcp_tools:
            tool = IntegratedTool(
                name=f"mcp:{mcp_tool.server_name}:{mcp_tool.name}",  # 添加前缀避免冲突
                description=mcp_tool.description,
                parameters=mcp_tool.parameters,
                origin="mcp",
                server_name=mcp_tool.server_name
            )
            self.integrated_tools.append(tool)
            
        logger.info(f"加载了 {len([t for t in self.integrated_tools if t.origin == 'mcp'])} 个MCP工具")
        
    async def add_mcp_server(self, name: str, config: dict) -> None:
        """添加MCP服务器"""
        if not self.mcp_manager:
            await self.initialize()
            
        await self.mcp_manager.add_mcp_server(name, config)
        
        # 重新加载MCP工具
        await self._reload_mcp_tools()
        
    async def remove_mcp_server(self, name: str) -> None:
        """移除MCP服务器"""
        if not self.mcp_manager:
            return
            
        await self.mcp_manager.remove_mcp_server(name)
        
        # 移除相关工具
        self.integrated_tools = [
            tool for tool in self.integrated_tools
            if not (tool.origin == "mcp" and tool.server_name == name)
        ]
        
    async def _reload_mcp_tools(self) -> None:
        """重新加载MCP工具"""
        # 移除所有MCP工具
        self.integrated_tools = [
            tool for tool in self.integrated_tools
            if tool.origin != "mcp"
        ]
        
        # 重新加载
        await self._load_mcp_tools()
        
    async def call_tool(self, tool_name: str, arguments: dict) -> Any:
        """调用工具（本地或MCP）"""
        # 查找工具
        tool = None
        for t in self.integrated_tools:
            if t.name == tool_name and t.active:
                tool = t
                break
                
        if not tool:
            raise ValueError(f"未找到工具: {tool_name}")
            
        if tool.origin == "local":
            return await self._call_local_tool(tool_name, arguments)
        elif tool.origin == "mcp":
            return await self._call_mcp_tool(tool, arguments)
        else:
            raise ValueError(f"未知的工具来源: {tool.origin}")
            
    async def _call_local_tool(self, tool_name: str, arguments: dict) -> Any:
        """调用本地工具"""
        if not FUNCTION_REGISTRY_AVAILABLE:
            raise RuntimeError("Function Registry不可用，无法调用本地工具")

        try:
            return await FunctionRegistry.call(tool_name, **arguments)
        except Exception as e:
            logger.error(f"调用本地工具 {tool_name} 失败: {e}")
            raise
            
    async def _call_mcp_tool(self, tool: IntegratedTool, arguments: dict) -> Any:
        """调用MCP工具"""
        if not self.mcp_manager:
            raise RuntimeError("MCP管理器未初始化")
            
        # 提取实际的工具名称（去掉前缀）
        actual_tool_name = tool.name.split(":")[-1]
        
        try:
            return await self.mcp_manager.call_tool(actual_tool_name, arguments)
        except Exception as e:
            logger.error(f"调用MCP工具 {tool.name} 失败: {e}")
            raise
            
    def get_available_tools(self) -> List[IntegratedTool]:
        """获取所有可用工具"""
        return [tool for tool in self.integrated_tools if tool.active]
        
    def get_tools_openai_format(self) -> List[Dict[str, Any]]:
        """获取OpenAI格式的工具列表"""
        return [tool.to_openai_format() for tool in self.get_available_tools()]
        
    def get_tools_anthropic_format(self) -> List[Dict[str, Any]]:
        """获取Anthropic格式的工具列表"""
        return [tool.to_anthropic_format() for tool in self.get_available_tools()]
        
    def get_tool_by_name(self, name: str) -> Optional[IntegratedTool]:
        """根据名称获取工具"""
        for tool in self.integrated_tools:
            if tool.name == name:
                return tool
        return None
        
    def get_local_tools(self) -> List[IntegratedTool]:
        """获取本地工具"""
        return [tool for tool in self.integrated_tools if tool.origin == "local"]
        
    def get_mcp_tools(self) -> List[IntegratedTool]:
        """获取MCP工具"""
        return [tool for tool in self.integrated_tools if tool.origin == "mcp"]
        
    def get_tools_by_server(self, server_name: str) -> List[IntegratedTool]:
        """获取指定服务器的工具"""
        return [
            tool for tool in self.integrated_tools 
            if tool.origin == "mcp" and tool.server_name == server_name
        ]
        
    async def cleanup(self) -> None:
        """清理资源"""
        if self.mcp_manager:
            await self.mcp_manager.cleanup()
            
        self.integrated_tools.clear()
        self._initialized = False
        logger.info("MCP Function Calling集成器已清理")

# 全局集成器实例
_integrator: Optional[MCPFunctionCallIntegrator] = None

async def get_mcp_integrator() -> MCPFunctionCallIntegrator:
    """获取全局MCP集成器实例"""
    global _integrator
    if _integrator is None:
        _integrator = MCPFunctionCallIntegrator()
        await _integrator.initialize()
    return _integrator

async def cleanup_mcp_integrator() -> None:
    """清理全局MCP集成器"""
    global _integrator
    if _integrator:
        await _integrator.cleanup()
        _integrator = None
