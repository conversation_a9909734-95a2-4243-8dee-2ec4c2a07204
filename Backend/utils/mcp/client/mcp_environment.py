"""
MCP环境检测和适配模块
自动检测WSL2、Linux板卡等不同环境，并提供相应的配置适配
"""

import os
import sys
import platform
import logging
import yaml
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class EnvironmentType(Enum):
    """环境类型枚举"""
    WSL2 = "wsl2"
    LINUX_BOARD = "linux_board"
    LINUX_DESKTOP = "linux_desktop"
    DOCKER = "docker"
    UNKNOWN = "unknown"

@dataclass
class EnvironmentInfo:
    """环境信息"""
    type: EnvironmentType
    platform: str
    architecture: str
    python_version: str
    has_audio: bool
    has_gpio: bool
    has_display: bool
    is_embedded: bool
    memory_mb: int

class MCPEnvironmentDetector:
    """MCP环境检测器"""
    
    def __init__(self):
        """初始化检测器"""
        self.env_info = None
        self._detect_environment()
    
    def _detect_environment(self) -> EnvironmentInfo:
        """检测当前环境"""
        logger.info("检测运行环境...")
        
        # 基础信息
        platform_name = platform.system()
        architecture = platform.machine()
        python_version = platform.python_version()
        
        # 检测环境类型
        env_type = self._detect_environment_type()
        
        # 检测硬件能力
        has_audio = self._check_audio_capability()
        has_gpio = self._check_gpio_capability()
        has_display = self._check_display_capability()
        is_embedded = self._is_embedded_linux()
        memory_mb = self._get_memory_info()
        
        self.env_info = EnvironmentInfo(
            type=env_type,
            platform=platform_name,
            architecture=architecture,
            python_version=python_version,
            has_audio=has_audio,
            has_gpio=has_gpio,
            has_display=has_display,
            is_embedded=is_embedded,
            memory_mb=memory_mb
        )
        
        logger.info(f"环境检测完成: {env_type.value}")
        return self.env_info
    
    def _detect_environment_type(self) -> EnvironmentType:
        """检测环境类型"""
        # 检测WSL2
        if self._is_wsl2():
            return EnvironmentType.WSL2
        
        # 检测Docker
        if self._is_docker():
            return EnvironmentType.DOCKER
        
        # 检测Linux板卡
        if platform.system() == "Linux":
            if self._is_embedded_linux():
                return EnvironmentType.LINUX_BOARD
            else:
                return EnvironmentType.LINUX_DESKTOP
        
        return EnvironmentType.UNKNOWN
    
    def _is_wsl2(self) -> bool:
        """检测是否为WSL2环境"""
        try:
            # 检查/proc/version文件
            with open('/proc/version', 'r') as f:
                version_info = f.read().lower()
                return 'microsoft' in version_info and 'wsl2' in version_info
        except:
            return False
    
    def _is_docker(self) -> bool:
        """检测是否为Docker环境"""
        try:
            # 检查/.dockerenv文件
            return os.path.exists('/.dockerenv')
        except:
            return False
    
    def _is_embedded_linux(self) -> bool:
        """检测是否为嵌入式Linux设备"""
        # ARM架构通常表示嵌入式设备
        arch = platform.machine().lower()
        if arch in ['aarch64', 'armv7l', 'armv6l']:
            return True
        
        # 检查设备树信息
        try:
            if os.path.exists('/proc/device-tree/model'):
                with open('/proc/device-tree/model', 'r') as f:
                    model = f.read().lower()
                    # 常见的嵌入式设备标识
                    embedded_keywords = ['raspberry', 'jetson', 'beagle', 'orange']
                    return any(keyword in model for keyword in embedded_keywords)
        except:
            pass
        
        # 检查内存大小（小于4GB可能是嵌入式设备）
        try:
            import psutil
            memory_gb = psutil.virtual_memory().total / (1024**3)
            return memory_gb < 4
        except:
            pass
        
        return False
    
    def _check_audio_capability(self) -> bool:
        """检查音频能力"""
        try:
            # 检查ALSA设备
            if os.path.exists('/proc/asound/cards'):
                with open('/proc/asound/cards', 'r') as f:
                    return len(f.read().strip()) > 0
        except:
            pass
        
        # 检查PulseAudio
        try:
            import subprocess
            result = subprocess.run(['pulseaudio', '--check'], 
                                  capture_output=True, timeout=5)
            return result.returncode == 0
        except:
            pass
        
        return False
    
    def _check_gpio_capability(self) -> bool:
        """检查GPIO能力"""
        # 检查GPIO设备文件
        gpio_paths = ['/dev/gpiochip0', '/sys/class/gpio']
        return any(os.path.exists(path) for path in gpio_paths)
    
    def _check_display_capability(self) -> bool:
        """检查显示能力"""
        # 检查DISPLAY环境变量
        if os.environ.get('DISPLAY'):
            return True
        
        # 检查Wayland
        if os.environ.get('WAYLAND_DISPLAY'):
            return True
        
        # 检查framebuffer设备
        return os.path.exists('/dev/fb0')
    
    def _get_memory_info(self) -> int:
        """获取内存信息（MB）"""
        try:
            import psutil
            return int(psutil.virtual_memory().total / (1024**2))
        except ImportError:
            try:
                # 从/proc/meminfo读取
                with open('/proc/meminfo', 'r') as f:
                    for line in f:
                        if line.startswith('MemTotal:'):
                            kb = int(line.split()[1])
                            return kb // 1024
            except:
                pass
        except Exception:
            pass

        return 0
    
    def get_environment_info(self) -> EnvironmentInfo:
        """获取环境信息"""
        return self.env_info
    
    def is_development_environment(self) -> bool:
        """是否为开发环境"""
        return self.env_info.type in [EnvironmentType.WSL2, EnvironmentType.DOCKER]
    
    def is_production_environment(self) -> bool:
        """是否为生产环境"""
        return self.env_info.type == EnvironmentType.LINUX_BOARD
    
    def should_enable_audio(self) -> bool:
        """是否应该启用音频功能"""
        return self.env_info.has_audio and not self.is_development_environment()
    
    def should_enable_hardware(self) -> bool:
        """是否应该启用硬件功能"""
        return self.env_info.has_gpio and self.is_production_environment()

# 全局环境检测器实例
_detector = None

def get_environment_detector() -> MCPEnvironmentDetector:
    """获取全局环境检测器实例"""
    global _detector
    if _detector is None:
        _detector = MCPEnvironmentDetector()
    return _detector

class MCPConfigAdapter:
    """MCP配置适配器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化适配器"""
        self.detector = MCPEnvironmentDetector()
        self.config_path = Path(config_path) if config_path else Path(__file__).parent.parent / "mcp_config.yaml"
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "environment": {"auto_detect": True, "type": "auto"},
            "mcp_server": {"name": "ASR-LLM-TTS MCP Server"},
            "platform_adaptation": {}
        }
    
    def get_adapted_config(self) -> Dict[str, Any]:
        """获取适配后的配置"""
        env_info = self.detector.get_environment_info()
        adapted_config = self.config.copy()
        
        # 根据环境类型适配配置
        env_config = self._get_environment_config(env_info.type)
        
        # 更新模块启用状态
        self._adapt_modules(adapted_config, env_info)
        
        # 更新日志级别
        if 'mcp_server' not in adapted_config:
            adapted_config['mcp_server'] = {}
        if 'logging' not in adapted_config['mcp_server']:
            adapted_config['mcp_server']['logging'] = {}
        
        adapted_config['mcp_server']['logging']['level'] = env_config.get('log_level', 'INFO')
        
        # 添加环境信息
        adapted_config['runtime_environment'] = {
            'type': env_info.type.value,
            'platform': env_info.platform,
            'architecture': env_info.architecture,
            'has_audio': env_info.has_audio,
            'has_gpio': env_info.has_gpio,
            'memory_mb': env_info.memory_mb
        }
        
        return adapted_config
    
    def _get_environment_config(self, env_type: EnvironmentType) -> Dict[str, Any]:
        """获取环境特定配置"""
        env_configs = self.config.get('environment', {})
        
        if env_type == EnvironmentType.WSL2:
            return env_configs.get('development', {})
        elif env_type == EnvironmentType.LINUX_BOARD:
            return env_configs.get('production', {})
        else:
            return env_configs.get('testing', {})
    
    def _adapt_modules(self, config: Dict[str, Any], env_info: EnvironmentInfo):
        """适配模块配置"""
        modules = config.get('mcp_server', {}).get('modules', {})
        
        for module_name, module_config in modules.items():
            # 检查模块的启用条件
            enabled_on = module_config.get('enabled_on', [])
            
            if enabled_on:
                # 根据环境条件决定是否启用
                should_enable = False
                
                if 'production' in enabled_on and env_info.type == EnvironmentType.LINUX_BOARD:
                    should_enable = True
                elif 'development' in enabled_on and env_info.type == EnvironmentType.WSL2:
                    should_enable = True
                
                module_config['enabled'] = should_enable
            
            # 特殊模块的额外检查
            if module_name == 'audio_control':
                module_config['enabled'] = module_config.get('enabled', True) and env_info.has_audio
            elif module_name == 'hardware_interface':
                module_config['enabled'] = module_config.get('enabled', True) and env_info.has_gpio

# 全局实例
_config_adapter = None

def get_mcp_config_adapter() -> MCPConfigAdapter:
    """获取全局配置适配器实例"""
    global _config_adapter
    if _config_adapter is None:
        _config_adapter = MCPConfigAdapter()
    return _config_adapter

def get_adapted_mcp_config() -> Dict[str, Any]:
    """获取适配后的MCP配置"""
    return get_mcp_config_adapter().get_adapted_config()

def get_environment_info() -> EnvironmentInfo:
    """获取环境信息"""
    return get_environment_detector().get_environment_info()

def is_wsl2_environment() -> bool:
    """是否为WSL2环境"""
    return get_environment_info().type == EnvironmentType.WSL2

def is_linux_board_environment() -> bool:
    """是否为Linux板卡环境"""
    return get_environment_info().type == EnvironmentType.LINUX_BOARD

def should_enable_audio_features() -> bool:
    """是否应该启用音频功能"""
    return get_environment_detector().should_enable_audio()

def should_enable_hardware_features() -> bool:
    """是否应该启用硬件功能"""
    return get_environment_detector().should_enable_hardware()

# 使用示例
if __name__ == "__main__":
    # 测试环境检测
    detector = MCPEnvironmentDetector()
    env_info = detector.get_environment_info()
    
    print(f"环境类型: {env_info.type.value}")
    print(f"平台: {env_info.platform}")
    print(f"架构: {env_info.architecture}")
    print(f"音频支持: {env_info.has_audio}")
    print(f"GPIO支持: {env_info.has_gpio}")
    print(f"内存: {env_info.memory_mb}MB")
    
    # 测试配置适配
    adapter = MCPConfigAdapter()
    config = adapter.get_adapted_config()
    print(f"\n适配后的配置:")
    print(f"日志级别: {config['mcp_server']['logging']['level']}")
    print(f"运行时环境: {config['runtime_environment']}")
