"""
MCP客户端管理器
支持连接外部MCP服务器，包括SSE、Streamable HTTP等传输方式
基于标准MCP协议实现，与现有Function Calling系统集成
"""

from __future__ import annotations
import json
import os
import asyncio
import logging
import aiohttp
import socket
from datetime import timedelta
from typing import Dict, List, Optional, Any, Literal
from dataclasses import dataclass
from contextlib import AsyncExitStack
from pathlib import Path
from urllib.parse import urlparse

# MCP相关导入
try:
    import mcp
    from mcp.client.sse import sse_client
    from mcp.client.streamable_http import streamablehttp_client
    MCP_AVAILABLE = True
except (ModuleNotFoundError, ImportError) as e:
    logging.warning(f"警告: 缺少依赖库 'mcp'，将无法使用 MCP 服务。错误: {e}")
    MCP_AVAILABLE = False

# 项目内部导入
from .mcp_environment import get_environment_info, EnvironmentInfo

logger = logging.getLogger(__name__)

# 默认MCP配置
DEFAULT_MCP_CONFIG = {"mcpServers": {}}

# 支持的JSON Schema数据类型
SUPPORTED_TYPES = [
    "string",
    "number", 
    "object",
    "array",
    "boolean",
]

@dataclass
class MCPTool:
    """MCP工具描述"""
    name: str
    parameters: Dict[str, Any]
    description: str
    server_name: str
    active: bool = True
    
    def to_function_calling_format(self) -> Dict[str, Any]:
        """转换为Function Calling格式"""
        return {
            "name": self.name,
            "parameters": self.parameters,
            "description": self.description,
            "origin": "mcp",
            "server_name": self.server_name
        }

class MCPClient:
    """MCP客户端封装"""

    def __init__(self):
        self.session: Optional[mcp.ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.name: Optional[str] = None
        self.active: bool = True
        self.tools: List[mcp.Tool] = []
        self.server_errlogs: List[str] = []
        self._streams_context = None

    async def health_check(self, config: dict, timeout: float = 3.0) -> bool:
        """快速健康检查，验证服务器是否可达

        Args:
            config: MCP服务器配置
            timeout: 检查超时时间（秒）

        Returns:
            bool: 服务器是否可达
        """
        if "url" not in config:
            # stdio连接无法进行网络健康检查
            return True

        url = config["url"]
        try:
            parsed_url = urlparse(url)
            host = parsed_url.hostname
            port = parsed_url.port

            if not host:
                logger.warning(f"无法解析主机名: {url}")
                return False

            # 默认端口
            if not port:
                port = 443 if parsed_url.scheme == "https" else 80

            # TCP连接检查
            logger.debug(f"检查服务器连通性: {host}:{port}")

            # 使用socket进行快速连接测试
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)

            try:
                result = sock.connect_ex((host, port))
                sock.close()

                if result == 0:
                    logger.debug(f"服务器 {host}:{port} 连通性检查通过")
                    return True
                else:
                    logger.warning(f"服务器 {host}:{port} 连接失败，错误码: {result}")
                    return False

            except socket.timeout:
                logger.warning(f"服务器 {host}:{port} 连接超时")
                return False
            except Exception as e:
                logger.warning(f"服务器 {host}:{port} 连接检查异常: {e}")
                return False

        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return False
        
    async def connect_to_server(self, config: dict, name: str) -> None:
        """连接到MCP服务器

        支持多种传输方式：
        1. URL + SSE: 使用Server-Sent Events连接
        2. URL + Streamable HTTP: 使用HTTP流连接
        3. Command + Args: 使用stdio连接本地进程

        Args:
            config: MCP服务器配置
            name: 服务器名称
        """
        if not MCP_AVAILABLE:
            raise RuntimeError("MCP库不可用，无法连接到MCP服务器")

        self.name = name
        cfg = config.copy()

        # 处理嵌套的mcpServers配置
        if "mcpServers" in cfg and len(cfg["mcpServers"]) > 0:
            key_0 = list(cfg["mcpServers"].keys())[0]
            cfg = cfg["mcpServers"][key_0]

        # 移除active标志
        cfg.pop("active", None)

        # 先进行健康检查（仅对URL连接）
        if "url" in cfg:
            logger.info(f"对MCP服务器 {name} 进行健康检查...")
            health_timeout = cfg.get("health_check_timeout", 3.0)

            if not await self.health_check(cfg, health_timeout):
                raise ConnectionError(f"MCP服务器 {name} 健康检查失败，服务器可能离线")

            logger.info(f"MCP服务器 {name} 健康检查通过，开始连接...")
            await self._connect_via_url(cfg)
        else:
            await self._connect_via_stdio(cfg, name)

        await self.session.initialize()
        logger.info(f"成功连接到MCP服务器: {name}")
        
    async def _connect_via_url(self, cfg: dict) -> None:
        """通过URL连接（SSE或Streamable HTTP）"""
        # 智能判断传输类型：优先使用transport字段，其次使用type字段，最后默认为sse
        transport_type = cfg.get("transport")
        if not transport_type:
            server_type = cfg.get("type", "sse")
            # 标准化类型名称
            if server_type == "streamable-http":
                transport_type = "streamable_http"
            else:
                transport_type = "sse"

        if transport_type == "streamable_http":
            # Streamable HTTP连接
            timeout = timedelta(seconds=cfg.get("timeout", 30))
            sse_read_timeout = timedelta(seconds=cfg.get("sse_read_timeout", 300))
            
            self._streams_context = streamablehttp_client(
                url=cfg["url"],
                headers=cfg.get("headers", {}),
                timeout=timeout,
                sse_read_timeout=sse_read_timeout,
                terminate_on_close=cfg.get("terminate_on_close", True),
            )
            read_s, write_s, _ = await self._streams_context.__aenter__()
            
            self.session = await self.exit_stack.enter_async_context(
                mcp.ClientSession(read_stream=read_s, write_stream=write_s)
            )
        else:
            # SSE连接（默认）
            self._streams_context = sse_client(
                url=cfg["url"],
                headers=cfg.get("headers", {}),
                timeout=cfg.get("timeout", 5),
                sse_read_timeout=cfg.get("sse_read_timeout", 300),
            )
            streams = await self._streams_context.__aenter__()
            
            self.session = await self.exit_stack.enter_async_context(
                mcp.ClientSession(*streams)
            )
            
    async def _connect_via_stdio(self, cfg: dict, name: str) -> None:
        """通过stdio连接本地进程"""
        server_params = mcp.StdioServerParameters(**cfg)
        
        def error_callback(msg: str):
            """处理MCP服务的错误日志"""
            self.server_errlogs.append(msg)
            logger.error(f"MCP服务器 {name} 错误: {msg}")
            
        stdio_transport = await self.exit_stack.enter_async_context(
            mcp.stdio_client(
                server_params,
                errlog=error_callback,
            ),
        )
        
        self.session = await self.exit_stack.enter_async_context(
            mcp.ClientSession(*stdio_transport)
        )
        
    async def list_tools_and_save(self) -> mcp.ListToolsResult:
        """列出所有工具并保存到self.tools"""
        if not self.session:
            raise RuntimeError("MCP会话未初始化")
            
        response = await self.session.list_tools()
        logger.debug(f"MCP服务器 {self.name} 工具列表: {response}")
        self.tools = response.tools
        return response
        
    async def call_tool(self, tool_name: str, arguments: dict) -> Any:
        """调用MCP工具"""
        if not self.session:
            raise RuntimeError("MCP会话未初始化")
            
        try:
            result = await self.session.call_tool(tool_name, arguments)
            logger.debug(f"MCP工具调用成功: {tool_name} -> {result}")
            return result
        except Exception as e:
            logger.error(f"MCP工具调用失败: {tool_name}, 错误: {e}")
            raise
            
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            # 先清理streams_context
            if self._streams_context:
                try:
                    await self._streams_context.__aexit__(None, None, None)
                except Exception:
                    pass  # 忽略streams清理错误

            # 再清理exit_stack
            try:
                await self.exit_stack.aclose()
            except Exception:
                pass  # 忽略exit_stack清理错误

        except Exception as e:
            # 只记录debug级别的日志，避免用户看到无害的错误
            logger.debug(f"清理MCP客户端资源时出错: {e}")


class MCPClientManager:
    """MCP客户端管理器

    负责管理多个MCP客户端连接，提供统一的工具调用接口
    """

    def __init__(self):
        self.clients: Dict[str, MCPClient] = {}
        self.tools: List[MCPTool] = []
        self.service_queue = asyncio.Queue()
        self.client_events: Dict[str, asyncio.Event] = {}  # 用于终止信号
        self.connection_events: Dict[str, asyncio.Event] = {}  # 用于连接完成信号
        self.env_info = get_environment_info()

    async def initialize(self) -> None:
        """初始化MCP客户端管理器"""
        logger.info("初始化MCP客户端管理器...")

        # 启动服务选择器任务
        asyncio.create_task(self._service_selector())

        # 加载MCP服务器配置
        await self._load_mcp_servers()

    async def _load_mcp_servers(self) -> None:
        """从配置文件加载MCP服务器，支持并发连接"""
        config_file = self._get_config_file_path()

        if not config_file.exists():
            # 创建默认配置文件
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(DEFAULT_MCP_CONFIG, f, ensure_ascii=False, indent=4)
            logger.info(f"创建默认MCP配置文件: {config_file}")
            return

        try:
            with open(config_file, "r", encoding="utf-8") as f:
                config = json.load(f)

            mcp_servers = config.get("mcpServers", {})

            if not mcp_servers:
                logger.info("没有配置MCP服务器")
                return

            logger.info(f"发现 {len(mcp_servers)} 个MCP服务器配置")

            # 收集活跃的服务器
            active_servers = []
            for name, server_config in mcp_servers.items():
                if server_config.get("active", True):
                    active_servers.append((name, server_config))
                else:
                    logger.info(f"跳过非活跃MCP服务器: {name}")

            if not active_servers:
                logger.info("没有活跃的MCP服务器")
                return

            # 并发连接所有活跃服务器
            logger.info(f"开始并发连接 {len(active_servers)} 个MCP服务器...")

            connection_tasks = []
            for name, server_config in active_servers:
                task = asyncio.create_task(
                    self._connect_server_with_timeout(name, server_config),
                    name=f"mcp-connect-{name}"
                )
                connection_tasks.append(task)

            # 等待所有连接完成（或超时）
            if connection_tasks:
                # 使用gather收集结果，但不抛出异常
                results = await asyncio.gather(*connection_tasks, return_exceptions=True)

                # 统计连接结果
                successful_connections = 0
                failed_connections = 0

                for i, result in enumerate(results):
                    server_name = active_servers[i][0]
                    if isinstance(result, Exception):
                        logger.warning(f"MCP服务器 {server_name} 连接失败: {result}")
                        failed_connections += 1
                    else:
                        successful_connections += 1

                logger.info(f"MCP服务器连接完成: 成功 {successful_connections} 个, 失败 {failed_connections} 个")

        except Exception as e:
            logger.error(f"加载MCP配置文件失败: {e}")

    async def _connect_server_with_timeout(self, name: str, server_config: dict) -> None:
        """带超时的服务器连接"""
        try:
            # 设置总体超时时间（包括健康检查和连接）
            total_timeout = server_config.get("timeout", 30) + 5  # 额外5秒缓冲

            await asyncio.wait_for(
                self.add_mcp_server(name, server_config),
                timeout=total_timeout
            )
            logger.debug(f"MCP服务器 {name} 连接成功")

        except asyncio.TimeoutError:
            logger.warning(f"MCP服务器 {name} 连接总体超时")
            raise
        except Exception as e:
            logger.warning(f"MCP服务器 {name} 连接异常: {e}")
            raise

    def _get_config_file_path(self) -> Path:
        """获取配置文件路径"""
        # 在MCP配置目录下查找配置文件
        return Path(__file__).parent.parent / "config" / "mcp_servers.json"

    async def add_mcp_server(self, name: str, config: dict) -> None:
        """添加MCP服务器"""
        # 创建一个事件来等待连接完成
        connection_event = asyncio.Event()
        self.connection_events[name] = connection_event

        await self.service_queue.put({
            "type": "init",
            "name": name,
            "config": config
        })

        # 等待连接完成（最多30秒）
        try:
            await asyncio.wait_for(connection_event.wait(), timeout=30.0)
            logger.info(f"MCP服务器 {name} 连接完成")
        except asyncio.TimeoutError:
            logger.warning(f"MCP服务器 {name} 连接超时")
        finally:
            # 清理连接事件
            if name in self.connection_events:
                del self.connection_events[name]

    async def remove_mcp_server(self, name: str) -> None:
        """移除MCP服务器"""
        await self.service_queue.put({
            "type": "terminate",
            "name": name
        })

    async def _service_selector(self) -> None:
        """服务选择器 - 统一管理MCP服务的启停"""
        while True:
            try:
                data = await self.service_queue.get()

                if data["type"] == "init":
                    if "name" in data:
                        event = asyncio.Event()
                        asyncio.create_task(
                            self._init_client_wrapper(
                                data["name"], data["config"], event
                            )
                        )
                        self.client_events[data["name"]] = event
                    else:
                        await self._load_mcp_servers()

                elif data["type"] == "terminate":
                    if "name" in data:
                        await self._terminate_client(data["name"])
                    else:
                        # 终止所有客户端
                        for name in list(self.clients.keys()):
                            await self._terminate_client(name)

            except Exception as e:
                logger.error(f"服务选择器处理请求时出错: {e}")

    async def _init_client_wrapper(self, name: str, config: dict, event: asyncio.Event) -> None:
        """初始化客户端的包装函数"""
        try:
            await self._init_client(name, config)
            await event.wait()  # 等待终止信号
            logger.info(f"收到MCP客户端 {name} 终止信号")
            await self._terminate_client(name)
        except Exception as e:
            logger.error(f"初始化MCP客户端 {name} 失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def _init_client(self, name: str, config: dict) -> None:
        """初始化单个MCP客户端"""
        try:
            # 先清理之前的客户端
            if name in self.clients:
                await self._terminate_client(name)

            client = MCPClient()
            self.clients[name] = client

            # 设置连接超时
            connection_timeout = config.get("timeout", 30)

            # 使用超时包装连接过程
            await asyncio.wait_for(
                client.connect_to_server(config, name),
                timeout=connection_timeout
            )

            tools_result = await client.list_tools_and_save()

            # 移除该服务器之前的工具
            self.tools = [
                tool for tool in self.tools
                if tool.server_name != name
            ]

            # 添加新工具
            for mcp_tool in client.tools:
                tool = MCPTool(
                    name=mcp_tool.name,
                    parameters=mcp_tool.inputSchema,
                    description=mcp_tool.description,
                    server_name=name
                )
                self.tools.append(tool)

            tool_names = [tool.name for tool in client.tools]
            logger.info(f"成功连接MCP服务器 {name}, 工具: {tool_names}")

            # 设置连接完成事件
            if name in self.connection_events:
                self.connection_events[name].set()

        except asyncio.TimeoutError:
            logger.warning(f"MCP服务器 {name} 连接超时，跳过该服务器")
            if name in self.clients:
                await self._terminate_client(name)
            # 设置事件，避免无限等待
            if name in self.connection_events:
                self.connection_events[name].set()
        except ConnectionError as e:
            logger.warning(f"MCP服务器 {name} 连接失败: {e}")
            if name in self.clients:
                await self._terminate_client(name)
            # 设置事件，避免无限等待
            if name in self.connection_events:
                self.connection_events[name].set()
        except Exception as e:
            logger.error(f"初始化MCP客户端 {name} 失败: {e}")
            if name in self.clients:
                await self._terminate_client(name)
            # 即使失败也要设置事件，避免无限等待
            if name in self.connection_events:
                self.connection_events[name].set()

    async def _terminate_client(self, name: str) -> None:
        """终止MCP客户端"""
        if name in self.clients:
            try:
                await self.clients[name].cleanup()
                del self.clients[name]
            except Exception as e:
                logger.warning(f"清理MCP客户端 {name} 时出错: {e}")

        # 移除相关工具
        self.tools = [
            tool for tool in self.tools
            if tool.server_name != name
        ]

        # 清理事件
        if name in self.client_events:
            self.client_events[name].set()
            del self.client_events[name]

        logger.info(f"已终止MCP客户端: {name}")

    async def call_tool(self, tool_name: str, arguments: dict) -> Any:
        """调用MCP工具"""
        # 查找工具所属的服务器
        tool = None
        for t in self.tools:
            if t.name == tool_name and t.active:
                tool = t
                break

        if not tool:
            raise ValueError(f"未找到MCP工具: {tool_name}")

        client = self.clients.get(tool.server_name)
        if not client:
            raise RuntimeError(f"MCP客户端 {tool.server_name} 不可用")

        return await client.call_tool(tool_name, arguments)

    def get_available_tools(self) -> List[MCPTool]:
        """获取所有可用的MCP工具"""
        return [tool for tool in self.tools if tool.active]

    def get_tools_for_function_calling(self) -> List[Dict[str, Any]]:
        """获取Function Calling格式的工具列表"""
        return [
            tool.to_function_calling_format()
            for tool in self.get_available_tools()
        ]

    async def cleanup(self) -> None:
        """清理所有资源"""
        for name in list(self.clients.keys()):
            await self._terminate_client(name)
