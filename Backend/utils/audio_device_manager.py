import asyncio
import logging
import threading
import time
from typing import Optional, Dict, Any, Callable
from enum import Enum
import sounddevice as sd

logger = logging.getLogger(__name__)

class AudioDeviceType(Enum):
    """音频设备类型"""
    TTS_PLAYER = "tts_player"
    MUSIC_PLAYER = "music_player"

class AudioDeviceManager:
    """
    音频设备管理器 - 协调多个音频播放器对同一设备的访问

    主要功能：
    1. 混合播放管理：支持TTS和音乐同时播放
    2. 音量协调：TTS播放时降低音乐音量，结束后恢复
    3. 优先级管理：TTS具有更高优先级
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return

        self._initialized = True
        self._device_lock = threading.Lock()  # 使用线程锁而不是异步锁
        self._active_users: set[AudioDeviceType] = set()  # 当前活跃的用户
        self._user_info: Dict[AudioDeviceType, Dict[str, Any]] = {}
        self._volume_callbacks: Dict[AudioDeviceType, Callable] = {}  # 音量控制回调
        self._original_volumes: Dict[AudioDeviceType, float] = {}  # 原始音量

        logger.info("音频设备管理器初始化完成（混合播放模式）")
    
    def register_volume_callback(self, device_type: AudioDeviceType, callback: Callable):
        """
        注册音量控制回调函数

        Args:
            device_type: 设备类型
            callback: 音量控制回调函数，接受一个float参数（音量值0.0-1.0）
        """
        self._volume_callbacks[device_type] = callback
        logger.info(f"已注册 {device_type.value} 的音量控制回调")
    
    def request_device_access_sync(self,
                                   device_type: AudioDeviceType,
                                   device_info: Dict[str, Any] = None,
                                   force_stop_others: bool = False) -> bool:
        """
        同步请求音频设备访问权限（混合播放模式）

        Args:
            device_type: 请求访问的设备类型
            device_info: 设备信息（用于日志）
            force_stop_others: 是否强制停止其他播放器（通常为False，支持混合播放）

        Returns:
            bool: 是否成功获得访问权限
        """
        with self._device_lock:
            # 添加到活跃用户列表
            self._active_users.add(device_type)
            self._user_info[device_type] = device_info or {}

            logger.info(f"设备访问权限已授予 {device_type.value}（混合播放模式）")
            logger.info(f"当前活跃用户: {[user.value for user in self._active_users]}")

            # 如果TTS开始播放，降低音乐音量
            if device_type == AudioDeviceType.TTS_PLAYER:
                self._adjust_music_volume_for_tts(True)

            return True

    def _adjust_music_volume_for_tts(self, tts_starting: bool):
        """
        为TTS播放智能调整音乐音量

        Args:
            tts_starting: True表示TTS开始播放，False表示TTS结束播放
        """
        if AudioDeviceType.MUSIC_PLAYER in self._active_users:
            music_callback = self._volume_callbacks.get(AudioDeviceType.MUSIC_PLAYER)
            if music_callback:
                try:
                    if tts_starting:
                        # TTS开始播放时的智能音量调整
                        # 优先从记录中获取音量，如果没有记录则使用默认值
                        current_music_volume = self._original_volumes.get(AudioDeviceType.MUSIC_PLAYER, 0.7)
                        tts_volume = self._original_volumes.get(AudioDeviceType.TTS_PLAYER, 0.5)
                        
                        # 存储TTS开始前的音乐音量，用于结束后恢复
                        self._music_volume_before_tts = current_music_volume
                        
                        # 智能音量调整逻辑
                        if current_music_volume <= tts_volume:
                            # 如果音乐音量已经比TTS音量小或相等，保持不变
                            adjusted_volume = current_music_volume
                            logger.info(f"TTS开始播放，音乐音量({current_music_volume:.2f})已小于等于TTS音量({tts_volume:.2f})，保持音乐音量不变")
                        else:
                            # 如果音乐音量比TTS音量大，调整到适当的背景音量
                            # 可以选择调整到TTS音量的60%，或者固定的0.3，取较小者
                            background_volume = min(0.3, tts_volume * 0.6)
                            adjusted_volume = background_volume
                            logger.info(f"TTS开始播放，音乐音量({current_music_volume:.2f})大于TTS音量({tts_volume:.2f})，降低音乐音量到{adjusted_volume:.2f}")
                        
                        # 执行音量调整
                        music_callback(adjusted_volume)
                    else:
                        # TTS结束，恢复音乐原始音量
                        # 使用TTS开始前保存的音量，如果没有则使用记录的原始音量
                        if hasattr(self, '_music_volume_before_tts'):
                            restore_volume = self._music_volume_before_tts
                            delattr(self, '_music_volume_before_tts')
                        else:
                            restore_volume = self._original_volumes.get(AudioDeviceType.MUSIC_PLAYER, 0.7)
                        
                        logger.info(f"TTS播放结束，恢复音乐音量到{restore_volume:.2f}")
                        music_callback(restore_volume)
                except Exception as e:
                    logger.error(f"调整音乐音量时出错: {e}")

    async def request_device_access(self,
                                  device_type: AudioDeviceType,
                                  device_info: Dict[str, Any] = None,
                                  force_stop_others: bool = True) -> bool:
        """
        异步请求音频设备访问权限（兼容现有代码）
        """
        return self.request_device_access_sync(device_type, device_info, force_stop_others)

    def release_device_access_sync(self, device_type: AudioDeviceType):
        """
        同步释放音频设备访问权限（混合播放模式）

        Args:
            device_type: 释放访问的设备类型
        """
        with self._device_lock:
            if device_type in self._active_users:
                self._active_users.remove(device_type)
                if device_type in self._user_info:
                    del self._user_info[device_type]

                logger.info(f"{device_type.value} 已释放设备访问权限")
                logger.info(f"剩余活跃用户: {[user.value for user in self._active_users]}")

                # 如果TTS结束播放，恢复音乐音量
                if device_type == AudioDeviceType.TTS_PLAYER:
                    self._adjust_music_volume_for_tts(False)
            else:
                logger.warning(f"{device_type.value} 尝试释放设备访问权限，但不在活跃用户列表中")

    async def release_device_access(self, device_type: AudioDeviceType):
        """
        异步释放音频设备访问权限（兼容现有代码）
        """
        self.release_device_access_sync(device_type)
    
    def get_active_users(self) -> set[AudioDeviceType]:
        """获取当前活跃用户列表"""
        return self._active_users.copy()

    def is_device_available(self) -> bool:
        """检查设备是否可用（混合播放模式下总是可用）"""
        return True

    def is_user_active(self, device_type: AudioDeviceType) -> bool:
        """检查指定用户是否活跃"""
        return device_type in self._active_users

    def set_original_volume(self, device_type: AudioDeviceType, volume: float):
        """设置设备的原始音量"""
        self._original_volumes[device_type] = volume
        logger.debug(f"设置 {device_type.value} 原始音量: {volume}")

    def update_music_player_volume(self, volume: float):
        """
        当用户手动设置音量时，更新音乐播放器的原始音量记录。
        """
        self.set_original_volume(AudioDeviceType.MUSIC_PLAYER, volume)
        logger.info(f"AudioDeviceManager 中记录的音乐播放器原始音量已更新为: {volume}")

# 全局设备管理器实例
audio_device_manager = AudioDeviceManager()
