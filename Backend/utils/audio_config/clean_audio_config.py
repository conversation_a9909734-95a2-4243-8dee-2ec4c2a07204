#!/usr/bin/env python3
"""
清理音频配置工具
移除错误的持久化配置
"""

import subprocess
import os

def run_command(cmd: str) -> dict:
    """运行系统命令"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        return {
            'success': result.returncode == 0,
            'stdout': result.stdout.strip(),
            'stderr': result.stderr.strip()
        }
    except Exception as e:
        return {'success': False, 'error': str(e)}

def clean_pulse_config():
    """清理PulseAudio配置"""
    print("🧹 清理PulseAudio持久化配置...")
    
    # 创建清理脚本
    script_content = '''#!/bin/bash
# 移除自定义音频配置
sed -i '/### Custom persistent audio device configuration/,+2d' /etc/pulse/default.pa

echo "配置已清理"
'''
    
    try:
        # 写入临时脚本
        script_path = '/tmp/clean_audio_config.sh'
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        # 设置执行权限
        os.chmod(script_path, 0o755)
        
        # 使用sudo执行脚本
        result = run_command(f'sudo bash {script_path}')
        
        # 清理临时文件
        os.remove(script_path)
        
        if result['success']:
            print("✅ 持久化配置已清理")
            
            # 重启PulseAudio
            restart_result = run_command('pulseaudio -k && pulseaudio --start')
            if restart_result['success']:
                print("✅ PulseAudio已重启")
            else:
                print("⚠️  请手动重启PulseAudio: pulseaudio -k && pulseaudio --start")
            
            return True
        else:
            print(f"❌ 清理配置失败: {result.get('stderr', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 清理配置失败: {e}")
        return False

def main():
    print("="*50)
    print("    清理音频配置工具")
    print("="*50)
    
    choice = input("确认要清理持久化音频配置吗? [y/N]: ").strip().lower()
    if choice != 'y':
        print("已取消")
        return
    
    if clean_pulse_config():
        print("\n🎉 配置清理完成!")
        print("💡 现在可以重新运行配置工具")
    else:
        print("\n❌ 配置清理失败")

if __name__ == "__main__":
    main()
