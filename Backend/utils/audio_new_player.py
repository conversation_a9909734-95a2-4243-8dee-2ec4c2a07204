import asyncio
import sounddevice as sd
import numpy as np
import queue
import logging
import time
from enum import Enum, auto
from typing import AsyncGenerator, Optional, Union
from .audio_device_manager import audio_device_manager, AudioDeviceType

# 设置日志记录器
logger = logging.getLogger(__name__)
# 如果没有为该 logger 配置 handler，则添加一个默认的，避免 "No handler found"
if not logger.hasHandlers():
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO) # 默认级别，可以根据需要调整

# --- 配置常量 ---
# 增加队列大小以处理潜在的网络抖动或处理延迟
DEFAULT_BUFFER_SIZE = 100
# 稍微增加预缓冲，确保开始播放时有足够数据
DEFAULT_MIN_PREBUFFER = 5
# 默认块大小
DEFAULT_BLOCK_SIZE = 1024
# 回调函数从队列获取数据的超时时间（秒）
CALLBACK_QUEUE_TIMEOUT = 0.1
# 清理资源时的等待超时时间（秒）
CLEANUP_TIMEOUT = 2.0

class PlayerState(Enum):
    """播放器状态枚举"""
    IDLE = auto()        # 初始或完全停止状态
    BUFFERING = auto()   # 正在缓冲初始数据
    PLAYING = auto()     # 正在播放音频
    STOPPING = auto()    # 正在停止过程中
    STOPPED = auto()     # 已停止，但资源可能未完全释放（过渡状态）
    ERROR = auto()       # 发生错误

class AudioPlayerError(Exception):
    """自定义音频播放器错误"""
    pass

class AudioNewPlayer:
    """
    一个新的异步音频播放器实现，专注于健壮性和流式处理。
    使用标准的 thread-safe queue.Queue 与 sounddevice 回调进行交互。
    """

    def __init__(self,
                 device: Optional[Union[int, str]] = None,
                 device_keyword: Optional[str] = None,
                 blocksize: int = DEFAULT_BLOCK_SIZE,
                 buffer_size: int = DEFAULT_BUFFER_SIZE,
                 min_prebuffer: int = DEFAULT_MIN_PREBUFFER,
                 volume: float = 1.0):
        """
        初始化播放器。

        Args:
            device: 输出设备索引或名称子串。None 表示默认设备。
            device_keyword: 设备名称关键词，用于按关键词查找设备。优先级高于device参数。
            blocksize: sounddevice 回调的块大小（帧数）。
            buffer_size: 内部音频块队列的最大容量。
            min_prebuffer: 开始播放前需要缓冲的最小块数。
            volume: 初始音量 (0.0 到 1.0)。
        """
        # 如果提供了设备关键词，则使用它来查找设备
        if device_keyword:
            self.device = self._select_output_device(device_keyword)
        else:
            self.device = device
            
        self.blocksize = blocksize
        self.buffer_size = buffer_size
        self.min_prebuffer = max(1, min_prebuffer) # 确保至少为1
        self._volume: float = np.clip(volume, 0.0, 1.0)
        # 预计算整数音量因子以提高回调性能
        self._volume_int: int = int(self._volume * 1024)
        # 标记音量是否发生变化，需要重新应用到现有数据
        self._volume_changed = False

        self._state: PlayerState = PlayerState.IDLE
        self._audio_queue: Optional[queue.Queue] = None
        self._stream: Optional[sd.OutputStream] = None
        self._feed_task: Optional[asyncio.Task] = None

        # 事件
        self._stop_event = asyncio.Event()          # 外部请求停止
        self._prebuffer_ready_event = asyncio.Event() # 预缓冲完成
        self._playback_finished_event = asyncio.Event() # sounddevice 流已结束
        self._error_event = asyncio.Event()           # 内部发生错误
        self._error_info: Optional[Exception] = None   # 存储错误信息

        # 音频属性
        self._sample_rate: Optional[int] = None
        self._channels: Optional[int] = None
        self._dtype = np.int16 # 固定为 int16 PCM
        self._bytes_per_frame = np.dtype(self._dtype).itemsize

        # --> Add this line
        self._loop: Optional[asyncio.AbstractEventLoop] = None

        # 回调状态
        self._callback_stats = {
            "underflows": 0,
            "queue_timeouts": 0,
            "chunks_processed": 0,
            "last_callback_time": 0.0
        }
        self._partial_bytes_from_queue: Optional[bytes] = None # 处理从队列取出但未完全用于回调的字节

        logger.info(f"音频播放器初始化完成。设备: {device or '默认'}, 音量: {self._volume:.2f}")

        # 注册到设备管理器
        audio_device_manager.register_volume_callback(
            AudioDeviceType.TTS_PLAYER,
            self.set_volume
        )
        
        # 记录初始音量到设备管理器
        audio_device_manager.set_original_volume(AudioDeviceType.TTS_PLAYER, self._volume)

    def _set_state(self, new_state: PlayerState):
        """安全地设置播放器状态并记录日志"""
        if self._state != new_state:
            logger.info(f"状态转换: {self._state.name} -> {new_state.name}")
            self._state = new_state

    def _stop_for_device_manager(self):
        """设备管理器调用的停止方法（同步版本）"""
        try:
            # 设置停止事件
            if self._stop_event:
                self._stop_event.set()
            logger.info("设备管理器请求停止播放")
        except Exception as e:
            logger.error(f"设备管理器停止播放时出错: {e}")

    async def _stop_for_device_manager_async(self):
        """设备管理器调用的异步停止方法"""
        try:
            await self.stop("设备管理器请求停止")
        except Exception as e:
            logger.error(f"设备管理器异步停止播放时出错: {e}")

    def _select_output_device(self, keyword: str) -> Optional[Union[int, str]]:
        """
        根据提供的关键词选择最佳的输出设备。

        选择逻辑:
        1. 优先查找名称中包含 `keyword` 的设备。
        2. 如果未找到，则回退查找包含 'pulse' 的设备。
        3. 如果两者都未找到，则返回 None，使用系统默认设备。

        Args:
            keyword: 用于查找设备名称的关键词 (忽略大小写)。

        Returns:
            找到的设备索引，或者 None (使用默认设备)。
        """
        try:
            devices = sd.query_devices()
            output_devices = [
                (i, dev) for i, dev in enumerate(devices) if dev['max_output_channels'] > 0
            ]

            logger.info("可用的输出设备:")
            for i, device_info in output_devices:
                logger.info(f"  设备 {i}: {device_info['name']} (默认采样率: {device_info['default_samplerate']})")

            # 1. 优先使用 `keyword` 查找
            if keyword:
                logger.info(f"正在根据关键词 '{keyword}' 查找设备...")
                for i, device_info in output_devices:
                    if keyword.lower() in device_info['name'].lower():
                        logger.info(f"已根据关键词 '{keyword}' 找到并选择设备 '{device_info['name']}' (索引: {i})")
                        return i
                logger.info(f"未找到与关键词 '{keyword}' 匹配的设备。")

            # 2. `keyword` 未找到，则查找 `PulseAudio` 作为备选
            logger.info("正在查找 'pulse' 设备作为备选...")
            for i, device_info in output_devices:
                if 'pulse' in device_info['name'].lower():
                    logger.info(f"未找到关键词匹配的设备，回退并选择 PulseAudio 设备 '{device_info['name']}' (索引: {i})")
                    return i

            # 3. 都未找到，则使用默认设备
            logger.warning("未找到关键词匹配或 PulseAudio 设备，将使用系统默认设备。")
            return None

        except Exception as e:
            logger.error(f"选择输出设备时出错: {e}", exc_info=True)
            return None
            
    @property
    def state(self) -> PlayerState:
        """获取当前播放器状态"""
        return self._state

    def set_volume(self, volume: float):
        """
        设置播放音量。可在播放期间调用。

        Args:
            volume: 新音量值 (0.0 到 1.0)。
        """
        old_volume = self._volume  # 保存旧音量以检测显著变化
        self._volume = np.clip(volume, 0.0, 1.0)
        self._volume_int = int(self._volume * 1024)
        logger.info(f"音量设置为: {self._volume:.2f} (因子: {self._volume_int})")
        
        # 同时更新AudioDeviceManager中的音量记录
        audio_device_manager.set_original_volume(AudioDeviceType.TTS_PLAYER, self._volume)
        
        # 记录音量是否发生了显著变化
        if abs(old_volume - self._volume) > 0.05:  # 降低阈值使小变化也能被捕获
            logger.info(f"音量变化显著 ({old_volume:.2f} -> {self._volume:.2f})，标记需要重新应用")
            self._volume_changed = True
            # 不再直接清除部分缓冲，而是在下一个回调中应用新音量

    def get_volume(self) -> float:
        """获取当前音量"""
        return self._volume

    def is_playing(self) -> bool:
        """检查播放器是否处于活动播放状态"""
        return self._state in (PlayerState.BUFFERING, PlayerState.PLAYING)

    def _numpy_from_bytes(self, audio_bytes: bytes) -> Optional[np.ndarray]:
        """将 int16 PCM 字节转换为 NumPy 数组。处理不完整帧。"""
        if not self._channels or not self._bytes_per_frame:
             logger.error("_numpy_from_bytes: 音频属性未设置")
             return None
        # 一帧的字节数 = 通道数 * 每个样本的字节数
        framesize = self._channels * self._bytes_per_frame
        num_bytes = len(audio_bytes)
        num_frames = num_bytes // framesize
        
        if num_frames == 0:
            logger.warning(f"_numpy_from_bytes: 字节数 ({num_bytes}) 不足一帧 ({framesize})")
            return None
            
        # 确保字节数是帧大小的整数倍
        trim_bytes = num_frames * framesize
        if trim_bytes < num_bytes:
            logger.debug(f"_numpy_from_bytes: 修剪字节 {num_bytes} -> {trim_bytes}")
            audio_bytes = audio_bytes[:trim_bytes]
            
        try:
            # 将字节转为NumPy数组 (int16)，并确保是可写的
            np_array = np.frombuffer(audio_bytes, dtype=np.int16).copy()
            # 不在这里进行reshape，让调用者处理
            return np_array
        except Exception as e:
            logger.error(f"_numpy_from_bytes: 转换字节为NumPy数组时出错: {e}")
            return None

    def _callback(self, outdata: np.ndarray, frames: int, time_info, status: sd.CallbackFlags):
        """sounddevice 音频回调函数 (在单独线程运行)"""
        try:
            callback_start_time = time.monotonic()
            # 优先检查停止事件，其次检查状态
            if self._stop_event.is_set() or self._state == PlayerState.STOPPING:
                logger.debug("_callback: 检测到停止事件或状态为 STOPPING，填充静音并返回")
                outdata.fill(0)
                # 确保回调被停止
                raise sd.CallbackStop
                return

            if status:
                logger.warning(f"_callback 状态: {status}")
                if status.output_underflow:
                    self._callback_stats["underflows"] += 1

            assert self._audio_queue is not None, "音频队列未初始化"
            assert self._channels is not None, "通道数未设置"
            assert self._sample_rate is not None, "采样率未设置"

            frames_needed = frames
            out_offset = 0
            
            # 捕获当前音量值，避免回调期间变化
            current_volume = self._volume
            volume_changed = self._volume_changed
            if volume_changed:
                self._volume_changed = False  # 重置标记
                logger.debug(f"_callback: 检测到音量变化，将应用新音量 {current_volume:.2f}")

            # 处理之前剩余的不完整数据块
            if self._partial_bytes_from_queue is not None:
                # 优先检查停止事件，其次检查状态
                if self._stop_event.is_set() or self._state == PlayerState.STOPPING:
                    logger.debug("_callback: 处理剩余数据前检测到停止事件或状态为 STOPPING")
                    outdata.fill(0)
                    raise sd.CallbackStop
                    return

                np_data = self._partial_bytes_from_queue
                # 正确计算处理帧数
                frames_in_partial = len(np_data) // (self._channels * 2)  # 2字节/样本
                frames_to_copy = min(frames_needed, frames_in_partial)

                try:
                    # 计算要复制的样本数
                    samples_to_copy = frames_to_copy * self._channels
                    # 先将 bytes 转为 int16 数组
                    if isinstance(np_data, bytes):
                        np_data = np.frombuffer(np_data, dtype=np.int16)
                    if samples_to_copy <= len(np_data):
                        data_slice = np_data[:samples_to_copy]
                        
                        # 应用音量控制 - 确保与下面处理新块的逻辑相同
                        if current_volume < 0.999:
                            data_slice = data_slice.astype(np.float32)
                            data_slice *= current_volume
                            data_slice = data_slice.astype(np.int16)
                        if current_volume < 0.001:
                            data_slice.fill(0)
                        
                        reshaped_data = data_slice.reshape(frames_to_copy, self._channels)
                        outdata[:frames_to_copy] = reshaped_data

                        out_offset = frames_to_copy
                        frames_needed -= frames_to_copy

                        # 保存剩余部分 (如果有)
                        if frames_to_copy < frames_in_partial:
                            self._partial_bytes_from_queue = np_data[samples_to_copy:]
                        else:
                            self._partial_bytes_from_queue = None
                    else:
                        logger.warning(f"_callback: 计算错误，数据可能不对齐: {samples_to_copy} > {len(np_data)}")
                        self._partial_bytes_from_queue = None
                except Exception as e:
                    logger.error(f"_callback: 处理剩余数据时出错: {e}")
                    self._partial_bytes_from_queue = None

            # 当还需要更多帧时，从队列获取
            while frames_needed > 0:
                # 优先检查停止事件，其次检查状态
                if self._stop_event.is_set() or self._state == PlayerState.STOPPING: # 再次检查，可能在循环中改变
                    logger.debug("_callback: 循环中检测到停止事件或状态为 STOPPING")
                    outdata[out_offset:].fill(0)
                    raise sd.CallbackStop
                    return

                try:
                    # 使用阻塞 get 并设置超时
                    chunk_bytes = self._audio_queue.get(block=True, timeout=CALLBACK_QUEUE_TIMEOUT)
                except queue.Empty:
                    self._callback_stats["queue_timeouts"] += 1
                    logger.warning(f"_callback: 音频队列超时 (第 {self._callback_stats['queue_timeouts']} 次)，填充静音。")
                    outdata[out_offset:].fill(0) # 填充剩余部分为静音
                    return # 超时则退出当前回调

                if chunk_bytes is None: # 流结束标记
                    logger.info("_callback: 收到流结束标记 (None)")
                    outdata[out_offset:].fill(0) # 填充剩余部分为静音
                    raise sd.CallbackStop # 明确停止流

                if not isinstance(chunk_bytes, bytes) or len(chunk_bytes) == 0:
                     logger.warning("_callback: 从队列收到空或无效数据")
                     continue # 获取下一块

                self._callback_stats["chunks_processed"] += 1
                
                # ===== 全新的音频数据处理部分，修复字节对齐和音量问题 =====
                try:
                    # 1. 保证字节长度是偶数 (对于 int16)
                    if isinstance(chunk_bytes, bytes):
                        bytes_len = len(chunk_bytes)
                        if bytes_len % 2 != 0:
                            # 如果是奇数字节，截掉最后一个字节
                            logger.warning(f"_callback: 修正奇数字节数据: {bytes_len} -> {bytes_len-1}")
                            chunk_bytes = chunk_bytes[:-1]
                            if len(chunk_bytes) == 0:
                                # 如果修正后没有数据，跳过这个块
                                continue
                    
                    # 2. 安全转换为NumPy数组
                    try:
                        audio_array = np.frombuffer(chunk_bytes, dtype=np.int16)
                    except ValueError as ve:
                        logger.error(f"_callback: 无法将数据转换为numpy数组: {ve}, 数据长度={len(chunk_bytes)}")
                        # 填充一部分静音并继续
                        frames_to_fill = min(frames_needed, 128)  # 填充一小段静音
                        outdata[out_offset:out_offset+frames_to_fill].fill(0)
                        out_offset += frames_to_fill
                        frames_needed -= frames_to_fill
                        continue
                    
                    # 3. 计算处理量 - 确保有足够的帧可以处理
                    if len(audio_array) < self._channels:
                        logger.warning(f"_callback: 数据太短: {len(audio_array)} < {self._channels}")
                        continue
                        
                    frames_in_chunk = len(audio_array) // self._channels
                    frames_to_copy = min(frames_needed, frames_in_chunk)
                    
                    if frames_to_copy <= 0:
                        logger.warning("_callback: 没有足够的帧可以复制")
                        continue
                        
                    samples_to_copy = frames_to_copy * self._channels
                    
                    # 4. 截取并应用音量
                    data_slice = audio_array[:samples_to_copy].copy()
                    
                    # 5. 音量控制 - 使用更强的实现
                    # 确保任何音量变化都能立即显著反映
                    # 注意：这里使用了前面捕获的 current_volume 变量，保持一致性
                    if current_volume < 0.999:  # 如果不是接近满音量
                        # 转float32，应用音量系数
                        data_float = data_slice.astype(np.float32)
                        data_float *= current_volume  # 直接乘以音量因子
                        data_slice = data_float.astype(np.int16)
                    
                    # 如果音量为0或接近0，直接填充静音
                    if current_volume < 0.001:  # 几乎静音
                        data_slice.fill(0)
                    
                    # 6. 重塑并复制到输出缓冲区
                    try:
                        data_reshaped = data_slice.reshape(frames_to_copy, self._channels)
                        outdata[out_offset:out_offset+frames_to_copy] = data_reshaped
                    except ValueError as ve:
                        logger.error(f"_callback: 重塑数据失败: {ve}")
                        # 填充相应区域为静音
                        outdata[out_offset:out_offset+frames_to_copy].fill(0)
                    
                    # 7. 更新计数和保存剩余数据
                    out_offset += frames_to_copy
                    frames_needed -= frames_to_copy
                    
                    if samples_to_copy < len(audio_array):
                        remaining_data = audio_array[samples_to_copy:].tobytes()
                        self._partial_bytes_from_queue = remaining_data
                        
                except Exception as e:
                    # 确保定义了frames_to_copy，避免后续引用错误
                    frames_to_use = min(frames_needed, 128)
                    logger.error(f"_callback: 处理音频数据时出错: {e}", exc_info=True)
                    outdata[out_offset:out_offset+frames_to_use].fill(0)
                    out_offset += frames_to_use
                    frames_needed -= frames_to_use
                # ===== 新音频处理结束 =====

            # 更新回调时间并检查性能
            callback_duration = time.monotonic() - callback_start_time
            self._callback_stats["last_callback_time"] = callback_duration
            if callback_duration > 0.01:  # 更长时间可能导致音频丢失
                logger.debug(f"_callback: 较长回调时间 {callback_duration:.4f}s")
            
        except sd.CallbackStop:
            # 正常停止回调，让它继续传播
            raise
        except AssertionError as ae:
            # 对于断言错误，我们记录它并停止回调
            logger.error(f"_callback 断言错误: {ae}")
            self._error_info = ae
            self._error_event.set()
            outdata.fill(0)
            raise sd.CallbackStop
        except Exception as e:
            # 对于其他意外错误，也记录它们并停止回调
            logger.error(f"_callback 意外错误: {e}")
            self._error_info = e
            self._error_event.set()
            outdata.fill(0)
            raise sd.CallbackStop

    def _stream_finished_callback(self):
        """由 sounddevice 在流停止或完成时调用（在单独线程）"""
        logger.info("_stream_finished_callback: 音频流已结束。")
        # 使用 call_soon_threadsafe 在主事件循环中设置事件
        # 使用预存的 loop
        if self._loop and not self._loop.is_closed():
            try:
                self._loop.call_soon_threadsafe(self._playback_finished_event.set)
            except Exception as loop_e:
                logger.error(f"尝试使用 call_soon_threadsafe 设置播放完成事件失败: {loop_e}")
        else:
            logger.error("无法获取有效的事件循环来设置播放完成事件")

    async def _feed_task_func(self, audio_generator: AsyncGenerator[bytes, None]):
        """异步任务：从生成器读取音频块并放入队列"""
        chunk_index = 0
        total_bytes_fed = 0
        feed_start_time = time.monotonic()

        try:
            logger.info("_feed_task: 开始运行")
            self._prebuffer_ready_event.clear()
            buffered_chunks = 0

            async for chunk_bytes in audio_generator:
                if self._stop_event.is_set():
                    logger.info("_feed_task: 检测到停止信号，退出。")
                    break

                if not isinstance(chunk_bytes, bytes) or len(chunk_bytes) == 0:
                    logger.warning("_feed_task: 收到空或无效数据块，跳过。")
                    continue

                chunk_index += 1
                chunk_len = len(chunk_bytes)
                total_bytes_fed += chunk_len
                logger.debug(f"_feed_task: 收到块 {chunk_index}, 大小: {chunk_len}, 总字节: {total_bytes_fed}")

                # 放入队列，处理队列满的情况
                while not self._stop_event.is_set():
                    try:
                        assert self._audio_queue is not None
                        self._audio_queue.put_nowait(chunk_bytes)
                        logger.debug(f"_feed_task: 块 {chunk_index} 已放入队列，当前大小: {self._audio_queue.qsize()}")
                        buffered_chunks += 1
                        # 检查是否达到预缓冲阈值
                        if not self._prebuffer_ready_event.is_set() and buffered_chunks >= self.min_prebuffer:
                             logger.info(f"_feed_task: 达到预缓冲目标 ({self.min_prebuffer} 块)")
                             self._prebuffer_ready_event.set()
                        break # 成功放入，跳出内层 while
                    except queue.Full:
                        if self._state == PlayerState.STOPPING: # 如果在等待时被停止
                             logger.info("_feed_task: 队列满且状态为 STOPPING，放弃放入")
                             break
                        logger.debug(f"_feed_task: 队列已满 (大小: {self.buffer_size})，等待空间...")
                        # 等待一小段时间，给回调消费数据的机会
                        await asyncio.sleep(0.01)
                    except Exception as e:
                         logger.error(f"_feed_task: 放入队列时发生意外错误: {e}", exc_info=True)
                         self._error_info = e
                         self._error_event.set()
                         return # 发生错误，退出任务

            # --- 循环结束后 ---
            if self._stop_event.is_set():
                logger.info("_feed_task: 因停止信号而结束。")
            else:
                # 正常结束，放入结束标记
                logger.info("_feed_task: 音频生成器已完成。")
                await self._put_sentinel_safe()

        except asyncio.CancelledError:
            logger.info("_feed_task: 任务被取消。")
        except Exception as e:
            logger.error(f"_feed_task: 发生意外错误: {e}", exc_info=True)
            self._error_info = e
            self._error_event.set()
        finally:
            feed_duration = time.monotonic() - feed_start_time
            logger.info(f"_feed_task: 结束。耗时: {feed_duration:.2f} 秒, 处理块数: {chunk_index}, 总字节: {total_bytes_fed}")
            # 确保即使发生错误或取消，预缓冲事件也被设置，防止 play_stream 卡住
            if not self._prebuffer_ready_event.is_set():
                logger.warning("_feed_task: 任务结束时预缓冲仍未完成，强制设置事件。")
                self._prebuffer_ready_event.set()
            # 确保即使发生错误或取消，也尝试放入结束标记（如果未停止）
            if not self._stop_event.is_set() and not self._error_event.is_set():
                 await self._put_sentinel_safe()


    async def _put_sentinel_safe(self):
        """安全地将 None（结束标记）放入队列，处理队列满"""
        if self._audio_queue is None:
             logger.warning("_put_sentinel_safe: 音频队列不存在")
             return

        logger.debug("_put_sentinel_safe: 尝试放入结束标记 (None)...")
        try:
             # 短暂等待，让回调可能处理掉一些数据
             await asyncio.sleep(0.05)
             put_attempts = 0
             max_attempts = 100 # 约等待 1 秒
             while put_attempts < max_attempts:
                  if self._stop_event.is_set():
                       logger.info("_put_sentinel_safe: 放入前检测到停止信号，放弃。")
                       return
                  try:
                       self._audio_queue.put_nowait(None)
                       logger.info("_put_sentinel_safe: 成功放入结束标记。")
                       return # 成功放入
                  except queue.Full:
                       put_attempts += 1
                       logger.debug(f"_put_sentinel_safe: 队列满，等待 ({put_attempts}/{max_attempts}) ...")
                       await asyncio.sleep(0.01)
             logger.warning("_put_sentinel_safe: 尝试多次后仍无法放入结束标记，队列可能阻塞。")
        except Exception as e:
             logger.error(f"_put_sentinel_safe: 放入结束标记时出错: {e}", exc_info=True)
             self._error_info = e
             self._error_event.set()


    async def _cleanup(self, reason: str = "未知"):
        """清理所有资源（任务、流、队列、事件）"""
        logger.info(f"开始清理资源，原因: {reason}")
        # 如果已经是 STOPPED 状态，不再设置 STOPPING
        if self._state != PlayerState.STOPPED:
            self._set_state(PlayerState.STOPPING)

        # 1. 停止并等待 feed_task
        if self._feed_task and not self._feed_task.done():
            logger.debug("取消 feed_task...")
            self._feed_task.cancel()
            try:
                await asyncio.wait_for(self._feed_task, timeout=CLEANUP_TIMEOUT / 2)
                logger.debug("feed_task 已结束。")
            except asyncio.CancelledError:
                logger.debug("feed_task 被成功取消。")
            except asyncio.TimeoutError:
                logger.warning("等待 feed_task 取消超时。")
            except Exception as e:
                logger.error(f"等待 feed_task 结束时出错: {e}")
        self._feed_task = None

        # 2. 停止并关闭 sounddevice 流
        stream = self._stream
        if stream:
             logger.debug("停止并关闭 sounddevice 流...")
             try:
                  # 强制中止播放更彻底
                  logger.info("强制关闭 sounddevice 流")
                  stream = self._stream  # 保存引用，避免并发修改
                  self._stream = None    # 立即清除引用
                  stream.abort()         # 中止流
                  stream.close(ignore_errors=True)  # 关闭流
             except Exception as e:
                  # sd.PortAudioError 可能会在流已关闭时抛出，但我们忽略它
                  logger.warning(f"关闭 sounddevice 流时发生错误（可能已关闭）: {e}")

        # 3. 清空并移除队列
        q = self._audio_queue
        if q:
             logger.debug("清空音频队列...")
             while not q.empty():
                  try: q.get_nowait()
                  except queue.Empty: break
             logger.debug(f"队列清空完成，剩余: {q.qsize()}")
        self._audio_queue = None

        # 4. 重置事件 (除了 error_event 可能需要保留)
        self._stop_event.clear()
        self._prebuffer_ready_event.clear()
        self._playback_finished_event.clear()
        # self._error_event.clear() # 保留错误状态直到下次播放

        # 5. 清理回调状态
        self._partial_bytes_from_queue = None
        self._callback_stats = { "underflows": 0, "queue_timeouts": 0, "chunks_processed": 0, "last_callback_time": 0.0 }

        # --> Add this line
        self._loop = None # 清理事件循环引用

        # 设置状态为 STOPPED 然后立即转为 IDLE
        self._set_state(PlayerState.STOPPED)
        await asyncio.sleep(0.1)  # 短暂等待确保状态转换完成
        self._set_state(PlayerState.IDLE)
        logger.info("资源清理完成，播放器已重置为IDLE状态。")


    async def play_stream(self,
                          audio_generator: AsyncGenerator[bytes, None],
                          sample_rate: int,
                          channels: int):
        """
        流式播放音频（完全异步）。

        Args:
            audio_generator: 异步生成器，产生字节流（PCM int16）
            sample_rate: 采样率 (Hz)
            channels: 通道数
        
        Raises:
            AudioPlayerError: 如果播放中发生错误
        """
        # 基本错误检查
        if self.state not in (PlayerState.IDLE, PlayerState.STOPPED):
            raise AudioPlayerError(f"播放器当前状态为 {self.state.name}，无法开始新的播放")

        # 请求音频设备访问权限
        device_info = {
            "sample_rate": sample_rate,
            "channels": channels,
            "device": self.device
        }

        access_granted = await audio_device_manager.request_device_access(
            AudioDeviceType.TTS_PLAYER,
            device_info,
            force_stop_others=True
        )

        if not access_granted:
            raise AudioPlayerError("无法获得音频设备访问权限")

        # 清理任何先前状态
        if self._audio_queue is not None or self._stream is not None:
            await self._cleanup("新播放请求")

        # 修正日志：显示当前的 self._volume
        logger.info(f"请求播放流: {sample_rate} Hz, {channels} 声道, 音量: {self._volume:.2f}")
        
        # 设置音频属性
        self._sample_rate = sample_rate
        self._channels = channels
        self._dtype = np.int16  # 固定使用16位有符号整数
        self._bytes_per_frame = channels * np.dtype(self._dtype).itemsize
        logger.debug(f"每帧字节数: {self._bytes_per_frame} (通道数: {channels}, 样本宽度: {np.dtype(self._dtype).itemsize})")
        
        # 创建消费者/生产者队列
        self._audio_queue = queue.Queue(maxsize=self.buffer_size)

        # 重置事件
        self._stop_event.clear()
        self._prebuffer_ready_event.clear()
        self._playback_finished_event.clear()
        self._error_event.clear()
        self._error_info = None
        self._partial_bytes_from_queue = None

        # 设置状态
        self._set_state(PlayerState.BUFFERING)
        
        # 存储事件循环引用以支持回调中的任务调度
        self._loop = asyncio.get_running_loop()
        
        # 预分配缓冲区以跟踪部分帧
        self._callback_stats = { "underflows": 0, "queue_timeouts": 0, "chunks_processed": 0, "last_callback_time": 0.0 }
        
        play_exception = None # 用于跟踪播放过程中的异常

        try:
            # 启动 feed 任务
            self._feed_task = asyncio.create_task(
                self._feed_task_func(audio_generator),
                name="audio_feeder"
            )

            # 等待预缓冲完成或出错或停止
            logger.info(f"等待预缓冲 ({self.min_prebuffer} 块)...")
            prebuffer_tasks = [
                asyncio.create_task(self._prebuffer_ready_event.wait()),
                asyncio.create_task(self._error_event.wait()),
                asyncio.create_task(self._stop_event.wait())
            ]
            try:
                done, pending = await asyncio.wait(
                    prebuffer_tasks,
                    return_when=asyncio.FIRST_COMPLETED
                )
            finally:
                for task in prebuffer_tasks:
                    if not task.done():
                        task.cancel()
            # ... 检查事件 ...
            if self._stop_event.is_set():
                raise AudioPlayerError("播放启动时被停止")
            if self._error_event.is_set():
                 raise AudioPlayerError(f"音频输入任务出错: {self._error_info}")
            if not self._prebuffer_ready_event.is_set():
                 raise AudioPlayerError("预缓冲等待超时")

            logger.info("预缓冲完成，启动 sounddevice 流...")
            self._set_state(PlayerState.PLAYING)

            # 创建并启动音频流
            self._stream = sd.OutputStream(
                samplerate=self._sample_rate,
                channels=self._channels,
                device=self.device,
                callback=self._callback,
                finished_callback=self._stream_finished_callback,
                blocksize=self.blocksize,
                dtype=self._dtype
            )
            self._stream.start()
            logger.info("sounddevice 流已启动。")

            # 等待播放完成或错误或停止
            logger.info("等待播放完成...")
            play_tasks = [
                asyncio.create_task(self._playback_finished_event.wait()),
                asyncio.create_task(self._error_event.wait()),
                asyncio.create_task(self._stop_event.wait())
            ]
            try:
                done, pending = await asyncio.wait(
                    play_tasks,
                    return_when=asyncio.FIRST_COMPLETED
                )
            finally:
                for task in play_tasks:
                    if not task.done():
                        task.cancel()
            # 检查结束原因
            if self._stop_event.is_set():
                 logger.info("播放过程中收到停止信号，立即中止播放。")
                 # 确保流被立即关闭
                 if self._stream:
                     try:
                         self._stream.abort()
                         self._stream.close(ignore_errors=True)
                         self._stream = None
                         logger.info("在检测到停止事件后强制关闭了音频流")
                     except Exception as e:
                         logger.warning(f"关闭流时异常: {e}")
                 play_exception = AudioPlayerError("播放过程中收到停止信号，立即中止播放。")
            elif self._error_event.is_set():
                 logger.error(f"播放过程中发生错误: {self._error_info}")
                 play_exception = AudioPlayerError(f"播放错误: {self._error_info}")
            elif self._playback_finished_event.is_set():
                 logger.info("播放正常完成。")
            else:
                 # 这不应该发生，但作为保险
                 logger.warning("等待播放结束时未设置任何预期事件。")

        except asyncio.CancelledError:
             logger.info("play_stream 任务被取消。")
             await self.stop("任务取消") # 确保清理
             raise # 重新抛出取消错误
        except AudioPlayerError as e:
             logger.error(f"播放失败: {e}")
             play_exception = e
             self._set_state(PlayerState.ERROR)
        except Exception as e:
             logger.error(f"播放过程中发生意外错误: {e}", exc_info=True)
             play_exception = RuntimeError(f"意外错误: {e}")
             self._set_state(PlayerState.ERROR)
        finally:
             # 无论如何都要清理资源
             # 强制关闭 sounddevice 流
             if self._stream:
                 try:
                     self._stream.abort()
                     self._stream.close(ignore_errors=True)
                     logger.info("finally关闭 sounddevice 流")
                 except Exception as e:
                     logger.warning(f"finally关闭流时异常: {e}")
             await self._cleanup(f"播放结束或异常 ({play_exception or '正常'})")

             # 释放音频设备访问权限
             try:
                 await audio_device_manager.release_device_access(AudioDeviceType.TTS_PLAYER)
             except Exception as e:
                 logger.warning(f"释放设备访问权限时出错: {e}")

             # 如果有错误，重新抛出
             if play_exception:
                 raise play_exception


    async def play(self, audio_data: bytes, sample_rate: int, channels: int):
        """
        异步播放单个完整的音频数据块。

        Args:
            audio_data: 包含 int16 PCM 音频数据的字节对象。
            sample_rate: 音频采样率 (Hz)。
            channels: 音频通道数。
        """
        if not isinstance(audio_data, bytes) or len(audio_data) == 0:
            raise ValueError("必须提供非空的音频数据字节")

        # 创建一个简单的异步生成器，只产生这一个块
        async def single_chunk_generator():
            yield audio_data
            logger.debug("单块生成器完成")

        logger.info(f"请求播放完整数据块: {len(audio_data)} bytes, {sample_rate} Hz, {channels} 声道")
        await self.play_stream(single_chunk_generator(), sample_rate, channels)


    async def stop(self, reason: str = "用户请求"):
        """
        异步停止当前播放（如果正在播放）。
        """
        if self.state not in (PlayerState.BUFFERING, PlayerState.PLAYING, PlayerState.STOPPING):
            logger.info(f"播放器未在活动状态 ({self.state.name})，无需停止。")
            # 如果是STOPPED状态，确保转为IDLE以便下次播放
            if self.state == PlayerState.STOPPED:
                logger.info("将播放器从STOPPED状态设为IDLE状态")
                self._set_state(PlayerState.IDLE)
            return

        logger.info(f"请求停止播放，原因: {reason}")
        # 重要：先设置状态为 STOPPING，然后设置停止事件
        # 这样回调函数可以立即检测到状态变化
        self._set_state(PlayerState.STOPPING)  # 立即设置状态
        self._stop_event.set()  # 设置停止信号

        # 强制清空音频队列
        if self._audio_queue:
            logger.debug("强制清空音频队列...")
            while not self._audio_queue.empty():
                try:
                    self._audio_queue.get_nowait()
                except Exception:
                    break
            # 尝试放入结束标记
            try:
                self._audio_queue.put_nowait(None)
                logger.debug("向音频队列添加结束标记")
            except Exception as e:
                logger.debug(f"添加队列结束标记失败: {e}")

        # 强制关闭 sounddevice 流
        if self._stream:
            try:
                # 强制中止播放更彻底
                logger.info("强制关闭 sounddevice 流")
                stream = self._stream  # 保存引用，避免并发修改
                self._stream = None    # 立即清除引用
                stream.abort()         # 中止流
                stream.close(ignore_errors=True)  # 关闭流
            except Exception as e:
                logger.warning(f"强制关闭流时异常: {e}")

        # 确保流关闭后将状态设置为 STOPPED
        if self.state == PlayerState.STOPPING:
            self._set_state(PlayerState.STOPPED)
        
        # 等待一小段时间以允许状态转换完成
        await asyncio.sleep(0.1)
        
        # 完全停止后自动转为IDLE状态
        if self.state == PlayerState.STOPPED:
            logger.info("播放器已停止，转为IDLE状态")
            self._set_state(PlayerState.IDLE)

        # _cleanup 将在 play_stream 退出时被调用

# --- End of AudioNewPlayer Class ---

# 可以在这里添加一个 main 函数进行测试，类似之前的版本
async def _test_main():
    logging.basicConfig(level=logging.DEBUG) # 测试时使用 DEBUG 级别
    logger.setLevel(logging.DEBUG)

    print("可用音频设备:")
    try:
        print(sd.query_devices())
    except Exception as e:
        print(f"查询设备失败: {e}")
        return

    target_device_index = None # 使用默认设备进行测试，或指定索引

    player = AudioNewPlayer(device=target_device_index, buffer_size=20, min_prebuffer=3)

    # --- 生成测试音频 (Sine Wave) ---
    sample_rate = 24000
    duration = 5 # 秒
    frequency = 440 # Hz
    t = np.linspace(0., duration, int(sample_rate * duration), endpoint=False)
    sine_wave = (0.5 * np.sin(2. * np.pi * frequency * t) * 32767).astype(np.int16)
    audio_data_full = sine_wave.tobytes()
    chunk_size_bytes = player.blocksize * player._bytes_per_frame * 1 # 单声道块大小

    # --- 测试 1: 播放完整数据块 ---
    print("\n--- 测试 1: 播放完整数据块 ---")
    try:
        await player.play(audio_data_full, sample_rate, channels=1)
        print("完整数据块播放完成。")
    except AudioPlayerError as e:
        print(f"播放失败: {e}")
    except Exception as e:
        print(f"发生意外错误: {e}", exc_info=True)

    await asyncio.sleep(1)

    # --- 测试 2: 流式播放 ---
    print("\n--- 测试 2: 流式播放 ---")
    async def audio_stream_gen(data: bytes, chunk_size: int):
        total_sent = 0
        for i in range(0, len(data), chunk_size):
            chunk = data[i:min(i + chunk_size, len(data))]
            if not chunk: break
            yield chunk
            total_sent += len(chunk)
            logger.debug(f"测试生成器: 发送 {len(chunk)} bytes, 总计 {total_sent}/{len(data)}")
            await asyncio.sleep(0.03) # 模拟网络延迟或处理时间
        logger.info("测试生成器: 所有数据已发送。")

    try:
        await player.play_stream(audio_stream_gen(audio_data_full, chunk_size_bytes), sample_rate, channels=1)
        print("流式播放完成。")
    except AudioPlayerError as e:
        print(f"流式播放失败: {e}")
    except Exception as e:
        print(f"发生意外错误: {e}", exc_info=True)

    await asyncio.sleep(1)

    # --- 测试 3: 播放中停止 ---
    print("\n--- 测试 3: 播放中停止 ---")
    try:
        print("开始长时间流式播放...")
        play_task = asyncio.create_task(
            player.play_stream(audio_stream_gen(audio_data_full * 3, chunk_size_bytes), sample_rate, channels=1)
        )
        await asyncio.sleep(3) # 播放一段时间
        print("请求停止播放...")
        await player.stop("测试停止")
        print("停止请求已发送。等待任务结束...")
        await play_task # 等待 play_stream 任务结束 (它应该会因为 stop 而退出)
        print("播放任务已结束。")
        assert player.state == PlayerState.STOPPED
    except AudioPlayerError as e:
        print(f"停止测试播放失败: {e}")
    except Exception as e:
        print(f"停止测试发生意外错误: {e}", exc_info=True)

    await asyncio.sleep(1)

    # --- 测试 4: 音量调节 ---
    print("\n--- 测试 4: 音量调节 ---")
    try:
        print("开始播放，将在中途改变音量...")
        async def volume_change_task():
             await asyncio.sleep(1.5)
             print(">>> 降低音量至 0.2")
             player.set_volume(0.2)
             await asyncio.sleep(1.5)
             print(">>> 升高音量至 0.9")
             player.set_volume(0.9)
             await asyncio.sleep(1.5)
             print(">>> 设置音量回 1.0")
             player.set_volume(1.0)

        # 并行运行播放和音量调节
        await asyncio.gather(
             player.play_stream(audio_stream_gen(audio_data_full, chunk_size_bytes), sample_rate, channels=1),
             volume_change_task()
        )
        print("音量调节测试播放完成。")

    except AudioPlayerError as e:
        print(f"音量调节测试失败: {e}")
    except Exception as e:
        print(f"音量调节测试发生意外错误: {e}", exc_info=True)


if __name__ == "__main__":
     # 取消注释以运行测试
     # asyncio.run(_test_main())
     pass # 默认不运行测试
