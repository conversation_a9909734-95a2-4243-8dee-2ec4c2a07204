"""
工具函数包
"""

from .config_loader import load_config, Config<PERSON>oa<PERSON>, TTSConfig

# 条件导入音频模块（在WSL2等环境中可能不可用）
try:
    from .audio_new_player import AudioNewPlayer
    _audio_available = True
except (ImportError, OSError) as e:
    # 在WSL2或缺少音频库的环境中，创建一个占位符类
    class AudioNewPlayer:
        def __init__(self, *args, **kwargs):
            raise RuntimeError(f"音频功能在当前环境中不可用: {e}")
    _audio_available = False

__all__ = ['load_config', 'ConfigLoader', 'TTSConfig', 'AudioNewPlayer', '_audio_available']