# Python 缓存
__pycache__/
*.py[cod]
*.so

# 虚拟环境
.venv/
venv/
ENV/
env/

# IDE 配置
.idea/
.vscode/
.settings/

# 日志文件
*.log

# Jupyter Notebook 检查点
.ipynb_checkpoints

# 系统文件
.DS_Store
Thumbs.db

# 临时文件
*.tmp
*.temp

# 训练/推理/测试输出
output/
outputs/
output_wavs_asr/
app/output_wavs_asr/
test/output/
test/output_wavs_asr/
test/logs/

# 模型权重和大文件
*.ckpt
*.pth
*.pt
*.h5
*.onnx
*.pb
*.tflite
*.npz
*.npy
*.tar
*.tar.gz
*.zip
*.7z
*.bin
*.param

# 音频、视频等大文件
*.wav
*.mp3
*.flac
*.ogg
*.aac
*.m4a
*.mp4
*.avi
*.mov

# model 目录下的模型文件和测试音频
Backend/model/asr/*
Backend/model/vad/*

# resource 目录下的音频
Backend/resource/*.mp3
Backend/resource/*.wav

# test/resource 下的音频
Backend/test/resource/*.mp3
Backend/test/resource/*.wav


Backend/memory-bank/

# 音频配置脚本生成的文件
# 音频诊断报告
Backend/system_audio_diagnosis.json
Backend/audio_diagnosis_*.json
Backend/utils/audio_config/system_audio_diagnosis.json
Backend/utils/audio_config/audio_diagnosis_*.json

# 配置文件备份
Backend/config.yaml.backup
Backend/config.yaml.backup.*
Backend/utils/audio_config/config.yaml.backup
Backend/utils/audio_config/config.yaml.backup.*

# 测试音频文件
Backend/test_music.wav
Backend/test_audio.wav
Backend/test_sound.wav
Backend/utils/audio_config/test_*.wav
Backend/utils/audio_config/test_*.mp3

# 音频配置脚本的临时文件
Backend/utils/audio_config/*.tmp
Backend/utils/audio_config/*.temp
Backend/utils/audio_config/temp_*

# PulseAudio配置文件（如果脚本生成）
Backend/utils/audio_config/default.pa
Backend/utils/audio_config/pulse_config_*

# 音频设备信息缓存
Backend/utils/audio_config/device_cache.json
Backend/utils/audio_config/audio_devices.json

# 前端开发相关忽略规则
# Node.js 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Next.js 构建输出
.next/
out/
build/
dist/

# 前端环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel 部署相关
.vercel
.turbo

# 前端测试覆盖率报告
coverage/

# 前端构建缓存
.swc/
.eslintcache
.stylelintcache
tsconfig.tsbuildinfo

# 前端包管理器锁定文件（可选，取决于团队策略）
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# 前端编辑器配置（如果不想共享）
# .prettierrc
# .eslintrc

# 脚本运行时生成的文件
# 进程ID文件
*.pid

# 运行时状态文件
Backend/server_status.json
Backend/audio_status.json
Backend/device_status.json

# 脚本日志文件
Backend/utils/audio_config/*.log
Backend/scripts/*.log
Backend/scripts/audio_tools/*.log

# 测试结果文件
Backend/test_results.json
Backend/audio_test_results.json
Backend/utils/audio_config/test_results.json

# 性能监控文件
Backend/performance_*.json
Backend/audio_performance_*.json

# 用户自定义配置（避免覆盖个人设置）
Backend/user_config.yaml
Backend/local_config.yaml
Backend/personal_*.yaml

# Backend environment file
Backend/.env

memory-bank/
